<?php
/**
 * AJAX endpoint for saving trading card settings
 */

require_once 'common.php';
require_once 'db_config.php';

// Enable CORS for GameBanana
tc_enable_cors();

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Require allowed origin
tc_require_allowed_origin();

// Enforce rate limiting
vb_enforce_rate_limit(30, 300); // 30 requests per 5 minutes

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    tc_json_response(['error' => 'Method not allowed'], 405);
}

// Get parameters
$profileId = isset($_POST['profile_id']) ? (int)$_POST['profile_id'] : 0;
$memberId = isset($_POST['_idMember']) ? (int)$_POST['_idMember'] : 0;
$showCollectionCount = $_POST['show_collection_count'] ?? 'true';
$notifyOnCollection = $_POST['notify_on_collection'] ?? 'true';

// Validate parameters
if ($profileId <= 0 || $memberId <= 0) {
    tc_json_response(['error' => 'Invalid parameters'], 400);
}

// Verify the member is the profile owner
if ($profileId !== $memberId) {
    tc_json_response(['error' => 'Unauthorized'], 403);
}

// Authenticate the member
vb_require_app_authentication($memberId, 1080);

// Validate settings values

$showCollectionCount = $showCollectionCount === 'true' ? 'true' : 'false';
$notifyOnCollection = $notifyOnCollection === 'true' ? 'true' : 'false';

// Prepare settings array
$settings = [
    'show_collection_count' => $showCollectionCount,
    'notify_on_collection' => $notifyOnCollection,
    'updated_at' => date('Y-m-d H:i:s')
];

try {
    global $db;
    
    // Save settings to database
    $settingsJson = json_encode($settings);
    $query = "INSERT INTO trading_card_settings (profile_id, settings) 
             VALUES (?, ?)
             ON DUPLICATE KEY UPDATE settings = ?";
    
    $stmt = $db->prepare($query);
    
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $db->error);
    }
    
    $stmt->bind_param('iss', $profileId, $settingsJson, $settingsJson);
    $result = $stmt->execute();
    $stmt->close();
    
    if ($result) {
        tc_json_response([
            'success' => true,
            'message' => 'Settings saved successfully',
            'settings' => $settings
        ]);
    } else {
        throw new Exception("Failed to save settings");
    }
    
} catch (Exception $e) {
    error_log("Save trading card settings error: " . $e->getMessage());
    tc_json_response(['error' => 'Failed to save settings'], 500);
}
?>

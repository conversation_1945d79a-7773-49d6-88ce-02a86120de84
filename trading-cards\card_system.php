<?php
/**
 * Core Trading Card System
 * Handles card generation, spawning, and collection mechanics
 */

require_once 'db_config.php';
require_once 'common.php';
require_once 'gamebanana_api.php';

/**
 * Get or create a member's trading card
 */
function tc_getMemberCard($memberId) {
    global $db;
    
    $memberId = (int)$memberId;
    if ($memberId <= 0) {
        return null;
    }
    
    // Check if card already exists
    $query = "SELECT * FROM member_cards WHERE member_id = ?";
    $stmt = $db->prepare($query);
    $stmt->bind_param('i', $memberId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        // Update spawn status if needed
        tc_updateSpawnStatus($memberId);
        
        // Get updated data
        $stmt->execute();
        $result = $stmt->get_result();
        $card = $result->fetch_assoc();
        
        // Add member details
        $memberDetails = tc_getMemberDetails($memberId);
        $card['member'] = $memberDetails;
        $card['rarity_config'] = tc_get_rarity_config($card['rarity']);
        
        return $card;
    }
    
    // Create new card
    return tc_createMemberCard($memberId);
}

/**
 * Create a new trading card for a member
 */
function tc_createMemberCard($memberId) {
    global $db;
    
    $memberId = (int)$memberId;
    if ($memberId <= 0) {
        return null;
    }
    
    // Generate random rarity and spawn rate
    $rarity = tc_generate_random_rarity();
    $spawnRate = tc_generate_spawn_rate($rarity);
    
    // Insert new card
    $query = "INSERT INTO member_cards (member_id, rarity, spawn_rate_minutes, is_spawned, last_spawn_time) 
              VALUES (?, ?, ?, 1, UTC_TIMESTAMP())";
    $stmt = $db->prepare($query);
    $stmt->bind_param('isi', $memberId, $rarity, $spawnRate);
    
    if ($stmt->execute()) {
        // Log the spawn event
        tc_logSpawnEvent($memberId);
        
        // Return the created card
        return tc_getMemberCard($memberId);
    }
    
    return null;
}

/**
 * Update spawn status for a card
 */
function tc_updateSpawnStatus($memberId) {
    global $db;
    
    $memberId = (int)$memberId;
    
    $query = "SELECT last_spawn_time, spawn_rate_minutes, is_spawned FROM member_cards WHERE member_id = ?";
    $stmt = $db->prepare($query);
    $stmt->bind_param('i', $memberId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        $shouldSpawn = tc_should_spawn_card($row['last_spawn_time'], $row['spawn_rate_minutes']);
        
        if ($shouldSpawn && !$row['is_spawned']) {
            // Spawn the card
            $updateQuery = "UPDATE member_cards SET is_spawned = 1, last_spawn_time = UTC_TIMESTAMP() WHERE member_id = ?";
            $updateStmt = $db->prepare($updateQuery);
            $updateStmt->bind_param('i', $memberId);
            $updateStmt->execute();
            
            // Log spawn event
            tc_logSpawnEvent($memberId);
        }
    }
}

/**
 * Collect a card
 */
function tc_collectCard($collectorId, $cardOwnerId) {
    global $db;
    
    $collectorId = (int)$collectorId;
    $cardOwnerId = (int)$cardOwnerId;
    
    if ($collectorId <= 0 || $cardOwnerId <= 0) {
        return ['success' => false, 'error' => 'Invalid member IDs'];
    }
    
    // Check if card exists and is spawned
    $query = "SELECT * FROM member_cards WHERE member_id = ? AND is_spawned = 1";
    $stmt = $db->prepare($query);
    $stmt->bind_param('i', $cardOwnerId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if (!$card = $result->fetch_assoc()) {
        return ['success' => false, 'error' => 'Card not available for collection'];
    }
    
    // Check if already collected
    $checkQuery = "SELECT id FROM card_collections WHERE collector_id = ? AND card_owner_id = ?";
    $checkStmt = $db->prepare($checkQuery);
    $checkStmt->bind_param('ii', $collectorId, $cardOwnerId);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();
    
    if ($checkResult->fetch_assoc()) {
        return ['success' => false, 'error' => 'Card already collected'];
    }
    
    // Start transaction
    $db->begin_transaction();
    
    try {
        // Add to collection
        $collectQuery = "INSERT INTO card_collections (collector_id, card_owner_id, rarity_at_collection) VALUES (?, ?, ?)";
        $collectStmt = $db->prepare($collectQuery);
        $collectStmt->bind_param('iis', $collectorId, $cardOwnerId, $card['rarity']);
        $collectStmt->execute();
        
        // Update card status (no longer spawned, increment collection count)
        $updateQuery = "UPDATE member_cards SET is_spawned = 0, total_collections = total_collections + 1 WHERE member_id = ?";
        $updateStmt = $db->prepare($updateQuery);
        $updateStmt->bind_param('i', $cardOwnerId);
        $updateStmt->execute();
        
        // Update spawn log
        $logQuery = "UPDATE card_spawn_log SET collected_by = ?, collected_at = UTC_TIMESTAMP() 
                     WHERE member_id = ? AND collected_by IS NULL ORDER BY spawned_at DESC LIMIT 1";
        $logStmt = $db->prepare($logQuery);
        $logStmt->bind_param('ii', $collectorId, $cardOwnerId);
        $logStmt->execute();
        
        $db->commit();
        
        return [
            'success' => true, 
            'card' => $card,
            'message' => 'Card collected successfully!'
        ];
        
    } catch (Exception $e) {
        $db->rollback();
        error_log("Card collection error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Collection failed'];
    }
}

/**
 * Get member's collection
 */
function tc_getMemberCollection($memberId, $limit = 50, $offset = 0) {
    global $db;
    
    $memberId = (int)$memberId;
    $limit = (int)$limit;
    $offset = (int)$offset;
    
    $query = "SELECT cc.*, mc.rarity as current_rarity, mc.total_collections 
              FROM card_collections cc 
              JOIN member_cards mc ON cc.card_owner_id = mc.member_id 
              WHERE cc.collector_id = ? 
              ORDER BY cc.collected_at DESC 
              LIMIT ? OFFSET ?";
    
    $stmt = $db->prepare($query);
    $stmt->bind_param('iii', $memberId, $limit, $offset);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $collection = [];
    while ($row = $result->fetch_assoc()) {
        $memberDetails = tc_getMemberDetails($row['card_owner_id']);
        $row['member'] = $memberDetails;
        $row['rarity_config'] = tc_get_rarity_config($row['rarity_at_collection']);
        $collection[] = $row;
    }
    
    return $collection;
}

/**
 * Get collection statistics
 */
function tc_getCollectionStats($memberId) {
    global $db;
    
    $memberId = (int)$memberId;
    
    $query = "SELECT 
                COUNT(*) as total_cards,
                COUNT(DISTINCT rarity_at_collection) as unique_rarities,
                SUM(CASE WHEN rarity_at_collection = 'common' THEN 1 ELSE 0 END) as common_count,
                SUM(CASE WHEN rarity_at_collection = 'uncommon' THEN 1 ELSE 0 END) as uncommon_count,
                SUM(CASE WHEN rarity_at_collection = 'rare' THEN 1 ELSE 0 END) as rare_count,
                SUM(CASE WHEN rarity_at_collection = 'epic' THEN 1 ELSE 0 END) as epic_count,
                SUM(CASE WHEN rarity_at_collection = 'legendary' THEN 1 ELSE 0 END) as legendary_count
              FROM card_collections 
              WHERE collector_id = ?";
    
    $stmt = $db->prepare($query);
    $stmt->bind_param('i', $memberId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    return $result->fetch_assoc();
}

/**
 * Log spawn event
 */
function tc_logSpawnEvent($memberId) {
    global $db;
    
    $query = "INSERT INTO card_spawn_log (member_id) VALUES (?)";
    $stmt = $db->prepare($query);
    $stmt->bind_param('i', $memberId);
    $stmt->execute();
}

/**
 * Get recently spawned cards for discovery
 */
function tc_getRecentlySpawnedCards($limit = 10) {
    global $db;
    
    $query = "SELECT mc.*, csl.spawned_at 
              FROM member_cards mc 
              JOIN card_spawn_log csl ON mc.member_id = csl.member_id 
              WHERE mc.is_spawned = 1 AND csl.collected_by IS NULL 
              ORDER BY csl.spawned_at DESC 
              LIMIT ?";
    
    $stmt = $db->prepare($query);
    $stmt->bind_param('i', $limit);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $cards = [];
    while ($row = $result->fetch_assoc()) {
        $memberDetails = tc_getMemberDetails($row['member_id']);
        $row['member'] = $memberDetails;
        $row['rarity_config'] = tc_get_rarity_config($row['rarity']);
        $cards[] = $row;
    }
    
    return $cards;
}
?>

<?php
// Set the default timezone
date_default_timezone_set('UTC');

// --- Response Headers ---

// Allow cross-origin requests from GameBanana
require_once __DIR__ . '/common.php';
vb_enable_cors_json(['https://gamebanana.com']);
header("Content-Type: application/json");

// Add HTTP Caching Headers (cache for 2 minutes)
$cacheDuration = 120; // seconds
header('Cache-Control: public, max-age=' . $cacheDuration);
header('Expires: ' . gmdate('D, d M Y H:i:s', time() + $cacheDuration) . ' GMT');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    // Just exit with 200 OK status
    http_response_code(200);
    exit;
}

// Enforce rate limiting (30 requests per 5 minute)
vb_enforce_rate_limit(30, 300);

require_once 'db_config.php';
require_once 'track_visitor.php';
require_once 'gamebanana_api.php';

// Enforce allowed Origin/Referer
vb_require_allowed_origin(['https://gamebanana.com']);

// Authenticate app access if user is logged in
$profileId = isset($_GET['profile_id']) ? (int)$_GET['profile_id'] : 0;
if ($profileId > 0) {
    vb_require_app_authentication($profileId, 487);
}

// Check for required parameters
if (!isset($_GET['profile_id'])) {
    vb_json_response(['error' => 'Missing required profile_id parameter'], 400);
}

// Get profile ID
$profileId = (int)$_GET['profile_id'];

// Validate profile ID
if ($profileId <= 0) {
    vb_json_response(['error' => 'Invalid profile ID'], 400);
}

// Calculate statistics
$stats = [
    'longest_streak' => 0,
    'longest_streak_user' => null,
    'total_visits' => getTotalVisits($profileId),
    'total_historical_visits' => getTotalHistoricalVisits($profileId),
    'active_days' => getActiveDays($profileId)
];

// Calculate longest streak
$longestStreakInfo = calculateLongestStreak($profileId);
$stats['longest_streak'] = $longestStreakInfo['streak'];
$stats['longest_streak_user'] = $longestStreakInfo['user'];

// Return the stats as JSON
vb_json_response($stats, 200, 120);

/**
 * Calculate the longest visit streak among all visitors
 * 
 * @param int $profileId The ID of the profile
 * @return array The longest streak and the user who has it
 */
function calculateLongestStreak($profileId) {
    global $db;
    
    try {
        // Fetch all unique visit dates per visitor for this profile, ordered for processing
        $query = "SELECT visitor_id, visit_date 
                  FROM (
                      SELECT 
                          visitor_id, 
                          DATE(visit_time) as visit_date 
                      FROM visit_history 
                      WHERE profile_id = ? 
                      GROUP BY visitor_id, DATE(visit_time)
                  ) AS unique_visits 
                  ORDER BY visitor_id ASC, visit_date DESC";
        
        $stmt = $db->prepare($query);
        
        if (!$stmt) {
            throw new Exception("Prepare failed for fetching visit history: " . $db->error);
        }
        
        $stmt->bind_param('i', $profileId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $overallLongestStreak = 0;
        $overallLongestStreakVisitorId = null;
        
        $currentVisitorId = null;
        $currentVisitorStreak = 0;
        $lastVisitDate = null;
        
        // Process results row by row
        while ($row = $result->fetch_assoc()) {
            $visitorId = (int)$row['visitor_id'];
            $visitDateStr = $row['visit_date'];
            $currentVisitDate = new DateTime($visitDateStr);

            // Check if this is a new visitor in the result set
            if ($visitorId !== $currentVisitorId) {
                // Starting new visitor, reset their streak count
                $currentVisitorId = $visitorId;
                $currentVisitorStreak = 1; 
                $lastVisitDate = $currentVisitDate;
            } else {
                // Same visitor, check for consecutive day
                if ($lastVisitDate) {
                    $dayDiff = $lastVisitDate->diff($currentVisitDate)->days;
                    
                    if ($dayDiff === 1) {
                        // Consecutive day, increment streak
                        $currentVisitorStreak++;
                        $lastVisitDate = $currentVisitDate; // Update last date for next comparison
                    } else if ($dayDiff > 1) {
                        // Gap detected, streak is broken for this user, restart
                        $currentVisitorStreak = 1;
                        $lastVisitDate = $currentVisitDate;
                    } // if dayDiff is 0, it means multiple visits on same day, streak doesn't change
                      // but we don't reset lastVisitDate here, keep the earliest date of the streak
                }
            }

            // Check if the current visitor's streak is the new overall longest
            if ($currentVisitorStreak > $overallLongestStreak) {
                $overallLongestStreak = $currentVisitorStreak;
                $overallLongestStreakVisitorId = $currentVisitorId;
            }
        }
        
        $stmt->close();

        // Get user details for the final longest streak holder AFTER processing all data
        $longestStreakUser = null;
        if ($overallLongestStreakVisitorId !== null) {
            $memberDetails = getMemberDetails($overallLongestStreakVisitorId);
            if ($memberDetails) {
                $longestStreakUser = [
                    'id' => $overallLongestStreakVisitorId,
                    'name' => $memberDetails['name']
                ];
            }
        }

        return [
            'streak' => $overallLongestStreak,
            'user' => $longestStreakUser
        ];
        
    } catch (Exception $e) {
        error_log("Error calculating longest streak for profile {$profileId}: " . $e->getMessage());
        return [
            'streak' => 0,
            'user' => null
        ];
    }
}

/**
 * Get the total number of visits to this profile
 * 
 * @param int $profileId The ID of the profile
 * @return int The total number of visits
 */
function getTotalVisits($profileId) {
    global $db;
    
    try {
        // Count all visits
        $query = "SELECT COUNT(*) as total_count FROM profile_visitors WHERE profile_id = ?";
        $stmt = $db->prepare($query);
        
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $db->error);
        }
        
        $stmt->bind_param('i', $profileId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            return (int)$row['total_count'];
        }
        
        return 0;
        
    } catch (Exception $e) {
        error_log("Error calculating total visits: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get the total number of historical visits to this profile
 * 
 * @param int $profileId The ID of the profile
 * @return int The total number of visits, including repeat visits
 */
function getTotalHistoricalVisits($profileId) {
    global $db;
    
    try {
        // Count all visits from the history table
        $query = "SELECT COUNT(*) as total_count FROM visit_history WHERE profile_id = ?";
        $stmt = $db->prepare($query);
        
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $db->error);
        }
        
        $stmt->bind_param('i', $profileId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            return (int)$row['total_count'];
        }
        
        return 0;
        
    } catch (Exception $e) {
        error_log("Error calculating total historical visits: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get the number of distinct active days for this profile (lifetime)
 */
function getActiveDays($profileId) {
    global $db;
    try {
        $query = "SELECT COUNT(*) as days_cnt FROM (SELECT DISTINCT DATE(visit_time) d FROM visit_history WHERE profile_id = ?) t";
        $stmt = $db->prepare($query);
        if (!$stmt) { throw new Exception("Prepare failed: " . $db->error); }
        $stmt->bind_param('i', $profileId);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
            return (int)$row['days_cnt'];
        }
        return 0;
    } catch (Exception $e) {
        error_log("Error calculating active days: " . $e->getMessage());
        return 0;
    }
}
?>

on: push
name: 🚀 Deploy website on push
jobs:
  web-deploy:
    name: 🎉 Deploy
    runs-on: ubuntu-latest
    steps:
    - name: 🚚 Get latest code
      uses: actions/checkout@v4
    
    - name: 📂 Sync files
      uses: SamKirkland/FTP-Deploy-Action@v4.3.5
      with:
        server: ftp.kurisumaki.se
        username: ${{ secrets.FTPUSERNAME }}
        password: ${{ secrets.FTPPASSWORD }}
        protocol: ftps
        server-dir: public_html/gamebanana/
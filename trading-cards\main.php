<!-- Link to external CSS file -->
<link rel="stylesheet" type="text/css" href="https://kurisumaki.se/gamebanana/trading-cards/trading-cards.css">

<?php
// Set the default timezone
date_default_timezone_set('UTC');

require_once __DIR__ . '/common.php';

// Allow requests from GameBanana for the main page
if (!vb_is_origin_allowed(['https://gamebanana.com'])) {
    echo "<!-- Warning: This app should be accessed through GameBanana.com -->";
}

// Enforce rate limiting (15 requests per 5 minute)
vb_enforce_rate_limit(15, 300);

require_once 'db_config.php';
require_once 'card_system.php';
require_once 'gamebanana_api.php';

// Get the current profile ID and visitor ID from GameBanana's API
$profileId = isset($_GET['_idProfile']) ? (int)$_GET['_idProfile'] : 0;
$visitorId = isset($_GET['_idMember']) ? (int)$_GET['_idMember'] : 0;

// Authenticate app access if user is logged in
if ($profileId > 0) {
    // First try to use cached token
    $cachedToken = vb_get_cached_token($profileId, 1080); // Trading cards app ID
    if ($cachedToken === null) {
        // If no cached token, authenticate with API
        if (!vb_authenticate_app($profileId, 1080)) {
            echo "<div class='Error'>App authentication failed. Please try again.</div>";
            exit;
        }
    }
}

// Check if current user is the owner or if we have valid IDs
$isOwner = ($visitorId === $profileId);
$validVisitor = ($visitorId > 0);
$validProfile = ($profileId > 0);

// Only proceed if we have a valid profile
if (!$validProfile) {
    echo "<div class='Error'>Invalid profile ID</div>";
    exit;
}

// Get or create the profile owner's trading card
$profileCard = tc_getMemberCard($profileId);

// Get settings from database or use defaults
$settings = getTradingCardSettings($profileId);
$cardSize = 'medium'; // Fixed to medium size for all profiles
$showCollectionCount = $settings['show_collection_count'] ?? 'true';
$notifyOnCollection = $settings['notify_on_collection'] ?? 'true';

// Compute dynamic base URL for API calls
$baseUrl = vb_compute_base_url();

// Get settings for this profile
function getTradingCardSettings($profileId) {
    global $db;

    try {
        $query = "SELECT settings FROM trading_card_settings WHERE profile_id = ?";
        $stmt = $db->prepare($query);

        if (!$stmt) {
            throw new Exception("Prepare failed: " . $db->error);
        }

        $stmt->bind_param('i', $profileId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {
            return json_decode($row['settings'], true);
        } else {
            // Default settings
            return [
                'show_collection_count' => 'true',
                'notify_on_collection' => 'true'
            ];
        }
    } catch (Exception $e) {
        error_log("Error retrieving trading card settings: " . $e->getMessage());
        return [
            'show_collection_count' => 'true',
            'notify_on_collection' => 'true'
        ];
    }
}

// Get member info for display
function getMemberInfo($memberId) {
    return tc_getMemberDetails($memberId);
}
?>

<!-- Mimicking GameBanana's module structure -->
<module id="TradingCards" class="PageModule StrangeBerryModule">
    <h2>
        <a href="https://gamebanana.com/apps/1080">Trading Cards</a>
        <?php if ($isOwner): ?>
        <a href="javascript:void(0);" id="TradingCardSettingsToggle" class="ModeSwitcher" title="Module Settings" onclick="toggleTradingCardSettingsPanel(event)">
            <span class="toggle-icon"><img src="https://i.imgur.com/vZWBXs3.png" alt=""></span>
        </a>
        <?php endif; ?>
    </h2>
    <div class="Tabs">
        <a href="javascript:void(0);" class="Tab TabActive" data-tab="card" aria-selected="true">Member Card</a>
        <?php if ($validVisitor): ?>
        <a href="javascript:void(0);" class="Tab" data-tab="collection" aria-selected="false">My Collection</a>
        <?php endif; ?>
    </div>

    <!-- Member Card Tab -->
    <div id="cardTabContent" class="Content PanelTransition">
        <?php if ($profileCard && $profileCard['is_spawned']): ?>
            <?php
            $member = $profileCard['member'];
            $rarityConfig = $profileCard['rarity_config'];

            // Cards are always public for all GameBanana members
            // App owners can collect their own cards, visitors can collect if not their own profile
            // But only users with initialized trading cards can collect
            $visitorHasCard = $validVisitor ? tc_getMemberCard($visitorId) : null;
            $canCollect = $validVisitor && $profileCard['is_spawned'] && $visitorHasCard;
            ?>
            <div class="TradingCard <?php echo $profileCard['rarity']; ?> card-size-<?php echo $cardSize; ?>"
                 <?php if ($validVisitor && $profileCard['is_spawned']): ?>
                 onclick="<?php echo $canCollect ? 'collectTradingCardWithNotifications(' . $profileId . ')' : 'redirectToTradingCardApp()'; ?>"
                 style="cursor: pointer;"
                 title="<?php echo $canCollect ? 'Click to collect this card' : 'Get the Trading Card app to collect cards'; ?>"
                 <?php endif; ?>>
                
                <div class="CardContent">
                    <!-- TCG Card Header -->
                    <div class="CardHeader">
                        <div class="CardName">
                            <a href="/members/<?php echo $profileId; ?>" style="color: inherit; text-decoration: none;">
                                <?php echo htmlspecialchars($member['name']); ?>
                            </a>
                        </div>
                        <?php if ($profileCard['total_collections'] > 0): ?>
                        <div class="CardLevel">
                            <?php echo $profileCard['total_collections']; ?>
                        </div>
                        <?php else: ?>
                        <div class="CardLevel new-card">
                            NEW
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- TCG Card Avatar -->
                    <div class="CardAvatar">
                        <img src="<?php echo $member['avatar']; ?>" alt="<?php echo $member['name']; ?>">
                    </div>

                    <!-- TCG Card Info -->
                    <div class="CardInfo">
                        <div class="CardRarity <?php echo $profileCard['rarity']; ?>">
                            <?php echo $rarityConfig['name']; ?>
                        </div>
                        <div class="CardStatus">
                            <?php if ($canCollect): ?>
                                <?php if ($validVisitor && $visitorId === $profileId): ?>
                                    Click to collect your own card
                                <?php else: ?>
                                    Click to collect
                                <?php endif; ?>
                            <?php elseif ($validVisitor && $profileCard['is_spawned'] && !$visitorHasCard): ?>
                                Get Trading Card app to collect
                            <?php else: ?>
                                Available for collection
                            <?php endif; ?>
                        </div>
                        <?php if ($showCollectionCount === 'true'): ?>
                        <div class="CardStats">
                            <?php if ($validVisitor && $visitorId === $profileId): ?>
                                Your card: Collected <?php echo $profileCard['total_collections']; ?> times
                                <br><small>Rarity: <?php echo $rarityConfig['name']; ?></small>
                            <?php else: ?>
                                Collected <?php echo $profileCard['total_collections']; ?> times
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>


                    </div>
                </div>
            </div>
        <?php elseif ($profileCard && !$profileCard['is_spawned']): ?>
            <?php if ($validVisitor && $visitorId === $profileId): ?>
                <!-- Profile owner can see their own spawn timing -->
                <div class="Empty">
                    Your card will respawn in:
                    <?php
                    $nextSpawn = tc_get_next_spawn_time($profileCard['last_spawn_time'], $profileCard['spawn_rate_minutes']);
                    echo tc_format_time_remaining(date('Y-m-d H:i:s', $nextSpawn));
                    ?>
                </div>
            <?php else: ?>
                <!-- Visitors see generic message -->
                <div class="Empty">No cards available right now. Check back later!</div>
            <?php endif; ?>
        <?php else: ?>
            <div class="Empty">No trading card found for this member.</div>
        <?php endif; ?>
    </div>

    <?php if ($validVisitor): ?>
    <!-- Collection Tab -->
    <div id="collectionTabContent" class="Content PanelTransition" style="display:none;">
        <div class="StatsPanel">
            <h3>Collection Statistics</h3>
            <div class="StatsGrid" id="collectionStats">
                <div class="StatItem">
                    <div class="StatValue" id="totalCards">—</div>
                    <div class="StatLabel">Total Cards</div>
                </div>
                <div class="StatItem">
                    <div class="StatValue" id="commonCards">—</div>
                    <div class="StatLabel">Common</div>
                </div>
                <div class="StatItem">
                    <div class="StatValue" id="uncommonCards">—</div>
                    <div class="StatLabel">Uncommon</div>
                </div>
                <div class="StatItem">
                    <div class="StatValue" id="rareCards">—</div>
                    <div class="StatLabel">Rare</div>
                </div>
                <div class="StatItem">
                    <div class="StatValue" id="epicCards">—</div>
                    <div class="StatLabel">Epic</div>
                </div>
                <div class="StatItem">
                    <div class="StatValue" id="legendaryCards">—</div>
                    <div class="StatLabel">Legendary</div>
                </div>
            </div>
        </div>
        <div id="collectionGrid" class="CollectionGrid">
            <div class="Loading">
                <img src="https://files.gamebanana.com/bitpit/load.gif" alt="Loading..."> Loading collection...
            </div>
        </div>
    </div>

    <?php endif; ?>

    <?php if ($isOwner): ?>
    <!-- Settings panel (overlay; independent of tabs) -->
    <div id="tradingCardSettingsPanel" class="SettingsPanel" style="display:none;">
        <h3>Module Settings</h3>

        <div class="SettingRow">
            <label for="showCollectionCount">Show Collection Count:</label>
            <select id="showCollectionCount">
                <option value="true" <?php echo $showCollectionCount == 'true' ? 'selected' : ''; ?>>Yes</option>
                <option value="false" <?php echo $showCollectionCount == 'false' ? 'selected' : ''; ?>>No</option>
            </select>
        </div>
        <div class="SettingRow">
            <label for="notifyOnCollection">Notify on Collection:</label>
            <select id="notifyOnCollection">
                <option value="true" <?php echo $notifyOnCollection == 'true' ? 'selected' : ''; ?>>Yes</option>
                <option value="false" <?php echo $notifyOnCollection == 'false' ? 'selected' : ''; ?>>No</option>
            </select>
        </div>
        <div id="saveFeedback" class="SaveFeedback" style="display:none;">
            <span class="feedbackText">Saving <img src="https://files.gamebanana.com/bitpit/load.gif"> </span>
        </div>
        <div class="SettingActions">
            <button onclick="saveTradingCardSettings()">Save</button>
            <button onclick="toggleTradingCardSettingsPanel(event)">Cancel</button>
        </div>
    </div>
    <?php endif; ?>
</module>

<script>
// Trading Cards App Namespace to prevent conflicts with other apps
window.TradingCardsApp = window.TradingCardsApp || {};

(function(TCA) {
    'use strict';

    // App-specific constants
    TCA.baseUrl = '<?php echo $baseUrl; ?>';
    TCA.profileId = <?php echo $profileId; ?>;
    TCA.visitorId = <?php echo $visitorId; ?>;
    TCA.tcSpawnCheckInterval = null;

    // Tab navigation for Trading Cards
    document.addEventListener('click', function(e) {
        const tab = e.target.closest('#TradingCards .Tab');
        if (!tab) return;

        const tabName = tab.getAttribute('data-tab');

        // Update tab states (only within TradingCards module)
        document.querySelectorAll('#TradingCards .Tab').forEach(t => {
            t.classList.remove('TabActive');
            t.setAttribute('aria-selected', 'false');
        });
        tab.classList.add('TabActive');
        tab.setAttribute('aria-selected', 'true');

        // Show/hide content
        const cardContent = document.getElementById('cardTabContent');
        const collectionContent = document.getElementById('collectionTabContent');
        const settingsToggle = document.getElementById('TradingCardSettingsToggle');
        const settingsPanel = document.getElementById('tradingCardSettingsPanel');

        if (tabName === 'card') {
            TCA.tcAnimateShow(cardContent);
            TCA.tcAnimateHide(collectionContent);
            if (settingsToggle) settingsToggle.style.display = '';
        } else if (tabName === 'collection') {
            TCA.tcAnimateHide(cardContent);
            TCA.tcAnimateShow(collectionContent);
            // Hide settings toggle when in collection tab
            if (settingsToggle) settingsToggle.style.display = 'none';
            // Close settings panel when entering collection tab
            if (settingsPanel && settingsPanel.style.display !== 'none') {
                TCA.tcAnimateHide(settingsPanel);
            }
            TCA.loadTradingCardCollection();
        }
    });

    // Ensure correct initial visibility of settings icon (card tab default)
    document.addEventListener('DOMContentLoaded', function() {
        const settingsToggle = document.getElementById('TradingCardSettingsToggle');
        if (settingsToggle) settingsToggle.style.display = '';
    });

    // Toggle settings panel with subtle animation
    TCA.toggleTradingCardSettingsPanel = function(event) {
        if (event) event.preventDefault();
        const panel = document.getElementById('tradingCardSettingsPanel');
        const isHidden = panel.style.display === 'none' || panel.style.display === '';
        if (isHidden) {
            panel.style.opacity = '0';
            panel.style.transform = 'translateY(6px)';
            panel.style.display = 'block';
            requestAnimationFrame(() => {
                panel.style.transition = 'opacity 180ms ease, transform 200ms ease';
                panel.style.opacity = '1';
                panel.style.transform = 'translateY(0)';
            });
        } else {
            panel.style.transition = 'opacity 150ms ease, transform 180ms ease';
            panel.style.opacity = '0';
            panel.style.transform = 'translateY(6px)';
            setTimeout(() => { panel.style.display = 'none'; }, 180);
        }
    };

    // Fallback functions for compatibility with visitors app (no longer needed with namespacing)
    if (typeof animateShow === 'undefined') {
        function animateShow(el) {
            if (!el) return;
            el.style.display = 'block';
            el.style.willChange = 'opacity, transform';
            el.style.opacity = '0';
            el.style.transform = 'translateY(6px)';
            requestAnimationFrame(() => {
                el.style.transition = 'opacity 180ms ease, transform 200ms ease';
                el.style.opacity = '1';
                el.style.transform = 'translateY(0)';
            });
        }
    }

    if (typeof animateHide === 'undefined') {
        function animateHide(el, after) {
            if (!el) { if (after) after(); return; }
            el.style.transition = 'opacity 150ms ease, transform 180ms ease';
            el.style.opacity = '0';
            el.style.transform = 'translateY(6px)';
            setTimeout(() => {
                el.style.display = 'none';
                if (after) after();
            }, 180);
        }
    }

    if (typeof saveSettings === 'undefined') {
        function saveSettings() {
            // Fallback for visitors app save settings
            console.log('Visitors app saveSettings called - fallback function');
            // This prevents the ReferenceError when visitors app tries to call saveSettings
            // The actual visitors app should define its own saveSettings function
        }
    }

    // Animation functions for Trading Cards
    TCA.tcAnimateShow = function(el) {
        if (!el) return;
        el.style.display = 'block';
        el.style.willChange = 'opacity, transform';
        el.style.opacity = '0';
        el.style.transform = 'translateY(6px)';
        requestAnimationFrame(() => {
            el.style.transition = 'opacity 180ms ease, transform 200ms ease';
            el.style.opacity = '1';
            el.style.transform = 'translateY(0)';
        });
    };

    TCA.tcAnimateHide = function(el, after) {
        if (!el) { if (after) after(); return; }
        el.style.transition = 'opacity 150ms ease, transform 180ms ease';
        el.style.opacity = '0';
        el.style.transform = 'translateY(6px)';
        setTimeout(() => {
            el.style.display = 'none';
            if (after) after();
        }, 180);
    };

    // Collect card function
    TCA.collectTradingCard = function(cardOwnerId) {
        const button = event.target;
        const originalText = button.textContent;

        button.disabled = true;
        button.textContent = 'Collecting...';

        const xhr = new XMLHttpRequest();
        xhr.open('POST', TCA.baseUrl + 'collect_card.php', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

        xhr.onload = function() {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        // Add collected animation
                        const card = button.closest('.TradingCard');
                        card.classList.add('collected');

                        // Update UI
                        button.textContent = 'Collected!';
                        button.style.background = '#4caf50';

                        // Update spawn indicator
                        const indicator = card.querySelector('.SpawnIndicator');
                        if (indicator) {
                            indicator.classList.add('unavailable');
                            indicator.title = 'Not currently available';
                        }

                        // Update status text
                        const statusElements = card.querySelectorAll('.CardStatus');
                        if (statusElements.length > 0) {
                            statusElements[0].textContent = response.card.spawn_rate_minutes + ' minutes until next spawn';
                        }

                        setTimeout(() => {
                            location.reload(); // Refresh to show updated state
                        }, 2000);
                    } else {
                        button.textContent = response.error || 'Collection failed';
                        button.style.background = '#f44336';
                        setTimeout(() => {
                            button.textContent = originalText;
                            button.style.background = '#4caf50';
                            button.disabled = false;
                        }, 3000);
                    }
                } catch (e) {
                    button.textContent = 'Error occurred';
                    button.style.background = '#f44336';
                    setTimeout(() => {
                        button.textContent = originalText;
                        button.style.background = '#4caf50';
                        button.disabled = false;
                    }, 3000);
                }
            } else {
                button.textContent = 'Network error';
                button.style.background = '#f44336';
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '#4caf50';
                    button.disabled = false;
                }, 3000);
            }
        };

        xhr.onerror = function() {
            button.textContent = 'Network error';
            button.style.background = '#f44336';
            setTimeout(() => {
                button.textContent = originalText;
                button.style.background = '#4caf50';
                button.disabled = false;
            }, 3000);
        };

        xhr.send(`collector_id=${TCA.visitorId}&card_owner_id=${cardOwnerId}`);
    };

    // Load collection
    TCA.loadTradingCardCollection = function() {
        const statsGrid = document.getElementById('collectionStats');
        const collectionGrid = document.getElementById('collectionGrid');

        const xhr = new XMLHttpRequest();
        xhr.open('GET', `${TCA.baseUrl}get_collection.php?member_id=${TCA.visitorId}`, true);

        xhr.onload = function() {
            if (xhr.status === 200) {
                try {
                    const data = JSON.parse(xhr.responseText);

                    // Update stats
                    document.getElementById('totalCards').textContent = data.stats.total_cards || 0;
                    document.getElementById('commonCards').textContent = data.stats.common_count || 0;
                    document.getElementById('uncommonCards').textContent = data.stats.uncommon_count || 0;
                    document.getElementById('rareCards').textContent = data.stats.rare_count || 0;
                    document.getElementById('epicCards').textContent = data.stats.epic_count || 0;
                    document.getElementById('legendaryCards').textContent = data.stats.legendary_count || 0;

                    // Update collection grid
                    if (data.collection && data.collection.length > 0) {
                        collectionGrid.innerHTML = data.collection.map(card => `
                            <div class="CollectionCard">
                                <div class="TradingCard ${card.rarity_at_collection}">
                                    <div class="CardContent">
                                        <div class="CardAvatar">
                                            <img src="${card.member.avatar}" alt="${card.member.name}">
                                        </div>
                                        <div class="CardInfo">
                                            <div class="CardName">${card.member.name}</div>
                                            <div class="CardRarity ${card.rarity_at_collection}">
                                                ${card.rarity_config.name}
                                            </div>
                                            <div class="CardStatus">
                                                Collected ${TCA.formatTime(card.collected_at)}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `).join('');
                    } else {
                        collectionGrid.innerHTML = '<div class="Empty">No cards collected yet. Visit other profiles to collect their cards!</div>';
                    }
                } catch (e) {
                    collectionGrid.innerHTML = '<div class="Empty">Error loading collection</div>';
                }
            } else {
                collectionGrid.innerHTML = '<div class="Empty">Error loading collection</div>';
            }
        };

        xhr.send();
    };

    // Save settings function
    TCA.saveTradingCardSettings = function() {
        const showCollectionCount = document.getElementById('showCollectionCount').value;
        const notifyOnCollection = document.getElementById('notifyOnCollection').value;

        // Show save feedback
        const saveFeedback = document.getElementById('saveFeedback');
        saveFeedback.style.display = 'flex';
        saveFeedback.querySelector('.feedbackText').textContent = 'Saving settings...';

        // Disable buttons during save
        const buttons = document.querySelectorAll('.SettingActions button');
        buttons.forEach(button => {
            button.disabled = true;
        });

        // Save settings via AJAX
        const xhr = new XMLHttpRequest();
        xhr.open('POST', TCA.baseUrl + 'save_settings.php', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

        xhr.onload = function() {
            if (xhr.status === 200) {
                // Update feedback message
                saveFeedback.querySelector('.feedbackText').innerHTML = 'Settings saved successfully <img src="https://files.gamebanana.com/bitpit/ok.gif">';

                // Hide settings panel and feedback after a short delay
                setTimeout(() => {
                    saveFeedback.style.display = 'none';
                    document.getElementById('tradingCardSettingsPanel').style.display = 'none';
                    // Re-enable buttons
                    buttons.forEach(button => {
                        button.disabled = false;
                    });
                }, 1200);
            } else {
                // Show error
                saveFeedback.querySelector('.feedbackText').innerHTML = 'Error saving settings <img src="https://files.gamebanana.com/bitpit/error.gif">';

                // Re-enable buttons
                buttons.forEach(button => {
                    button.disabled = false;
                });

                // Hide error after a delay
                setTimeout(() => {
                    saveFeedback.style.display = 'none';
                }, 3000);
            }
        };

        xhr.onerror = function() {
            // Show error
            saveFeedback.querySelector('.feedbackText').innerHTML = 'Network error. Please try again <img src="https://files.gamebanana.com/bitpit/error.gif">';

            // Re-enable buttons
            buttons.forEach(button => {
                button.disabled = false;
            });

            // Hide error after a delay
            setTimeout(() => {
                saveFeedback.style.display = 'none';
            }, 3000);
        };

        // Send the request with profile ID and settings
        xhr.send(`profile_id=${TCA.profileId}&show_collection_count=${showCollectionCount}&notify_on_collection=${notifyOnCollection}&_idMember=${TCA.visitorId}`);
    };

    // Format time helper
    TCA.formatTime = function(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;

        if (diff < 60000) return 'just now';
        if (diff < 3600000) return Math.floor(diff / 60000) + 'm ago';
        if (diff < 86400000) return Math.floor(diff / 3600000) + 'h ago';
        if (diff < 604800000) return Math.floor(diff / 86400000) + 'd ago';

        return date.toLocaleDateString();
    };

    // Auto-refresh spawn status every 30 seconds
    TCA.startTradingCardSpawnStatusCheck = function() {
        if (TCA.tcSpawnCheckInterval) {
            clearInterval(TCA.tcSpawnCheckInterval);
        }

        TCA.tcSpawnCheckInterval = setInterval(() => {
            TCA.checkTradingCardSpawnStatus();
        }, 30000); // Check every 30 seconds
    };

    TCA.checkTradingCardSpawnStatus = function() {
        const xhr = new XMLHttpRequest();
        xhr.open('GET', `${TCA.baseUrl}check_spawn_status.php?member_id=${TCA.profileId}`, true);

        xhr.onload = function() {
            if (xhr.status === 200) {
                try {
                    const status = JSON.parse(xhr.responseText);
                    TCA.updateTradingCardSpawnUI(status);
                } catch (e) {
                    console.error('Error parsing trading card spawn status:', e);
                }
            }
        };

        xhr.send();
    };

    TCA.updateTradingCardSpawnUI = function(status) {
        const indicator = document.querySelector('.SpawnIndicator');
        const statusElements = document.querySelectorAll('.CardStatus');
        const collectButton = document.querySelector('.CollectButton');

        if (indicator) {
            if (status.is_spawned) {
                indicator.classList.remove('unavailable');
                indicator.title = 'Available for collection';
            } else {
                indicator.classList.add('unavailable');
                indicator.title = 'Not currently available';
            }
        }

        if (statusElements.length > 0) {
            if (status.is_spawned) {
                statusElements[0].textContent = 'Available for collection';
            } else {
                statusElements[0].textContent = status.time_remaining;
            }
        }

        if (collectButton) {
            collectButton.disabled = !status.is_spawned;
            if (status.is_spawned) {
                collectButton.textContent = 'Collect Card';
                collectButton.style.background = '#4caf50';
            } else {
                collectButton.textContent = 'Not Available';
                collectButton.style.background = '#666';
            }
        }
    };

    // Initialize auto-refresh when page loads
    document.addEventListener('DOMContentLoaded', function() {
        TCA.startTradingCardSpawnStatusCheck();

        // Check spawn status immediately
        setTimeout(TCA.checkTradingCardSpawnStatus, 1000);
    });

    // Cleanup intervals when page unloads
    window.addEventListener('beforeunload', function() {
        if (TCA.tcSpawnCheckInterval) clearInterval(TCA.tcSpawnCheckInterval);
    });

    // Add notification system for card collections
    TCA.showTradingCardNotification = function(message, type = 'success') {
        // Create notification element
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            transition: all 0.3s ease;
            transform: translateX(100%);
            ${type === 'success' ? 'background: #4caf50;' : type === 'error' ? 'background: #f44336;' : 'background: #2196f3;'}
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    };

    // Enhanced collect card function with notifications for clickable cards
    TCA.collectTradingCardWithNotifications = function(cardOwnerId) {
        const card = event.target.closest('.TradingCard');
        const statusElement = card.querySelector('.CardStatus');
        const originalStatus = statusElement.textContent;

        // Prevent multiple clicks
        if (card.classList.contains('collecting')) {
            return;
        }

        card.classList.add('collecting');
        card.style.pointerEvents = 'none';
        statusElement.textContent = 'Collecting...';

        const xhr = new XMLHttpRequest();
        xhr.open('POST', TCA.baseUrl + 'collect_card.php', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

        xhr.onload = function() {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        TCA.showTradingCardNotification('Card collected successfully!', 'success');

                        // Add collected animation
                        card.classList.add('collected');
                        statusElement.textContent = 'Collected!';
                        statusElement.style.color = '#4caf50';

                        // Add collection effect
                        card.style.transform = 'scale(1.05)';
                        card.style.boxShadow = '0 0 30px rgba(76, 175, 80, 0.6)';

                        setTimeout(() => {
                            // Hide the card since it's no longer available
                            card.style.opacity = '0';
                            card.style.transform = 'scale(0.8)';

                            setTimeout(() => {
                                location.reload(); // Refresh to show updated state
                            }, 500);
                        }, 1500);
                    } else {
                        TCA.showTradingCardNotification(response.error || 'Collection failed', 'error');
                        statusElement.textContent = originalStatus;
                        card.classList.remove('collecting');
                        card.style.pointerEvents = 'auto';
                    }
                } catch (e) {
                    TCA.showTradingCardNotification('Error occurred during collection', 'error');
                    statusElement.textContent = originalStatus;
                    card.classList.remove('collecting');
                    card.style.pointerEvents = 'auto';
                }
            } else {
                TCA.showTradingCardNotification('Network error occurred', 'error');
                statusElement.textContent = originalStatus;
                card.classList.remove('collecting');
                card.style.pointerEvents = 'auto';
            }
        };

        xhr.onerror = function() {
            TCA.showTradingCardNotification('Network error occurred', 'error');
            statusElement.textContent = originalStatus;
            card.classList.remove('collecting');
            card.style.pointerEvents = 'auto';
        };

        xhr.send(`collector_id=${TCA.visitorId}&card_owner_id=${cardOwnerId}`);
    };

    // Redirect to trading card app for users without cards
    TCA.redirectToTradingCardApp = function() {
        TCA.showTradingCardNotification('You need the Trading Card app to collect cards!', 'info');
        setTimeout(() => {
            window.open('https://gamebanana.com/apps/1080', '_blank');
        }, 1500);
    };

    // Expose global functions for HTML onclick handlers
    window.toggleTradingCardSettingsPanel = TCA.toggleTradingCardSettingsPanel;
    window.collectTradingCard = TCA.collectTradingCard;
    window.saveTradingCardSettings = TCA.saveTradingCardSettings;
    window.collectTradingCardWithNotifications = TCA.collectTradingCardWithNotifications;
    window.redirectToTradingCardApp = TCA.redirectToTradingCardApp;

})(window.TradingCardsApp);
</script>

<?php
// Hourly heatmap for a profile over a period
// Params: profile_id (int), days (int, default 30)

date_default_timezone_set('UTC');
require_once __DIR__ . '/common.php';
vb_enable_cors_json(['https://gamebanana.com']);
header('Content-Type: application/json');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') { http_response_code(200); exit; }

// Enforce rate limiting (30 requests per 5 minute)
vb_enforce_rate_limit(30, 300);

require_once 'db_config.php';

vb_require_allowed_origin(['https://gamebanana.com']);

// Authenticate app access if user is logged in
$visitorId = isset($_GET['_idMember']) ? (int)$_GET['_idMember'] : 0;
$profileId = isset($_GET['_idProfile']) ? (int)$_GET['_idProfile'] : 0;

if ($profileId > 0) {
    vb_require_app_authentication($profileId, 487);
}

$days = isset($_GET['days']) ? (int)$_GET['days'] : 30;
if ($profileId <= 0) { vb_json_response(['error' => 'Invalid profile ID'], 400); }
if ($days < 1 || $days > 365) { $days = 30; }

try {
    global $db;
    $sinceExpr = "DATE_SUB(UTC_TIMESTAMP(), INTERVAL ? DAY)";
    $sql = "SELECT DAYOFWEEK(visit_time) AS dow, HOUR(visit_time) AS hr, COUNT(*) AS c
            FROM visit_history
            WHERE profile_id = ? AND visit_time >= $sinceExpr
            GROUP BY dow, hr";
    $stmt = $db->prepare($sql);
    if (!$stmt) { throw new Exception('Prepare failed: ' . $db->error); }
    $stmt->bind_param('ii', $profileId, $days);
    $stmt->execute();
    $res = $stmt->get_result();

    // Initialize 7x24 grid (1..7 -> Sun..Sat per MySQL), index 0..6 in array
    $grid = array_fill(0, 7, array_fill(0, 24, 0));
    $max = 0;
    while ($row = $res->fetch_assoc()) {
        $dow = (int)$row['dow']; // 1..7 (1=Sun)
        $hr = (int)$row['hr'];   // 0..23
        $cnt = (int)$row['c'];
        $rowIdx = $dow - 1;
        if ($rowIdx >= 0 && $rowIdx < 7 && $hr >= 0 && $hr < 24) {
            $grid[$rowIdx][$hr] = $cnt;
            if ($cnt > $max) { $max = $cnt; }
        }
    }
    $stmt->close();

    $today = new DateTime('now', new DateTimeZone('UTC'));
    $start = (clone $today)->modify('-' . ($days - 1) . ' days');

    vb_json_response([
        'profile_id' => $profileId,
        'days' => $days,
        'start_date' => $start->format('Y-m-d'),
        'end_date' => $today->format('Y-m-d'),
        'grid' => $grid,
        'max' => $max,
        'labels' => [
            'days' => ['Sun','Mon','Tue','Wed','Thu','Fri','Sat'],
            'hours' => range(0,23)
        ]
    ], 200, 120);
} catch (Exception $e) {
    error_log('Heatmap error: ' . $e->getMessage());
    vb_json_response(['error' => 'Server error'], 500);
}

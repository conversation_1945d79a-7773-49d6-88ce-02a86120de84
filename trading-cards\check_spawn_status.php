<?php
/**
 * AJAX endpoint for checking card spawn status
 */

require_once 'common.php';
require_once 'db_config.php';
require_once 'card_system.php';

// Enable CORS for GameBanana
tc_enable_cors();

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Require allowed origin
tc_require_allowed_origin();

// Enforce rate limiting
vb_enforce_rate_limit(120, 300); // 120 requests per 5 minutes (more frequent checks)

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    tc_json_response(['error' => 'Method not allowed'], 405);
}

// Get parameters
$memberId = isset($_GET['member_id']) ? (int)$_GET['member_id'] : 0;

// Validate parameters
if ($memberId <= 0) {
    tc_json_response(['error' => 'Invalid member ID'], 400);
}

try {
    // Update spawn status first
    tc_updateSpawnStatus($memberId);
    
    // Get current card status
    global $db;
    $query = "SELECT is_spawned, last_spawn_time, spawn_rate_minutes FROM member_cards WHERE member_id = ?";
    $stmt = $db->prepare($query);
    $stmt->bind_param('i', $memberId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        $nextSpawnTime = tc_get_next_spawn_time($row['last_spawn_time'], $row['spawn_rate_minutes']);
        
        tc_json_response([
            'is_spawned' => (bool)$row['is_spawned'],
            'next_spawn_time' => $nextSpawnTime,
            'time_remaining' => tc_format_time_remaining(date('Y-m-d H:i:s', $nextSpawnTime)),
            'spawn_rate_minutes' => $row['spawn_rate_minutes']
        ], 200, 30); // Cache for 30 seconds
    } else {
        tc_json_response(['error' => 'Card not found'], 404);
    }
    
} catch (Exception $e) {
    error_log("Check spawn status error: " . $e->getMessage());
    tc_json_response(['error' => 'Failed to check spawn status'], 500);
}
?>

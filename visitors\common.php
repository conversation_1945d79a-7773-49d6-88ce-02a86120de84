<?php
// Common utilities for Visitors app
// Set default timezone consistently
if (!ini_get('date.timezone')) {
    date_default_timezone_set('UTC');
}

// Format time similar to GameBanana
function vb_format_time(string $timestamp): string {
    $time = strtotime($timestamp);
    $diff = time() - $time;

    if ($diff < 60) {
        return 'now';
    } elseif ($diff < 3600) {
        $minutes = floor($diff / 60);
        return $minutes . 'm ago';
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return $hours . 'h ago';
    } elseif ($diff < 604800) {
        $days = floor($diff / 86400);
        return $days . 'd ago';
    } elseif ($diff < 2592000) {
        $weeks = floor($diff / 604800);
        return $weeks . 'w ago';
    }
    $months = floor($diff / 2592000);
    return $months . 'mo ago';
}

// Determine time class similar to GameBanana
function vb_time_class(string $timestamp): string {
    $time = strtotime($timestamp);
    $diff = time() - $time;

    if ($diff < 300) {
        return 'TimeStamp LessThan5minsOld';
    } elseif ($diff < 1800) {
        return 'TimeStamp LessThan30minsOld';
    } elseif ($diff < 3600) {
        return 'TimeStamp LessThan1HourOld';
    } elseif ($diff < 14400) {
        return 'TimeStamp LessThan4HoursOld';
    } elseif ($diff < 86400) {
        return 'TimeStamp LessThan1DayOld';
    }
    return 'TimeStamp OlderThan1Day';
}

// Basic JSON response helper (sets status and outputs JSON)
function vb_json_response($data, int $status = 200, ?int $cacheSeconds = null): void {
    http_response_code($status);
    header('Content-Type: application/json');
    if ($cacheSeconds !== null) {
        header('Cache-Control: public, max-age=' . $cacheSeconds);
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + $cacheSeconds) . ' GMT');
    }
    echo json_encode($data);
    exit;
}

// CORS helper for JSON APIs restricted to allowed origins
function vb_enable_cors_json(array $allowedOrigins = ['https://gamebanana.com']): void {
    $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
    if ($origin && in_array($origin, $allowedOrigins, true)) {
        header('Access-Control-Allow-Origin: ' . $origin);
    } else {
        // fallback to the primary allowed origin for legacy behavior
        header('Access-Control-Allow-Origin: https://gamebanana.com');
    }
    header('Vary: Origin');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');
    header('Access-Control-Max-Age: 3600');
}

// Check Origin and Referer against allowed list
function vb_is_origin_allowed(array $allowedOrigins = ['https://gamebanana.com']): bool {
    $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
    if ($origin && in_array($origin, $allowedOrigins, true)) {
        return true;
    }
    $referer = $_SERVER['HTTP_REFERER'] ?? '';
    if ($referer) {
        $ref = parse_url($referer);
        $refSchemeHost = ($ref['scheme'] ?? '') . '://' . ($ref['host'] ?? '');
        if (in_array($refSchemeHost, $allowedOrigins, true)) {
            return true;
        }
    }
    return false;
}

function vb_require_allowed_origin(array $allowedOrigins = ['https://gamebanana.com']): void {
    if (!vb_is_origin_allowed($allowedOrigins)) {
        vb_json_response(['error' => 'Forbidden origin'], 403);
    }
}

// Compute absolute base URL for this script directory
function vb_compute_base_url(): string {
    $isHttps = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ||
               (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == 443);
    $scheme = $isHttps ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $dir = rtrim(str_replace('\\', '/', dirname($_SERVER['SCRIPT_NAME'] ?? '/')), '/');
    return $scheme . '://' . $host . ($dir ? $dir . '/' : '/');
}

// Compute this server's origin (scheme + host)
function vb_self_origin(): string {
    $isHttps = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ||
               (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == 443);
    $scheme = $isHttps ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    return $scheme . '://' . $host;
}

// GameBanana App Authentication - returns token on success, false on failure
function vb_authenticate_app(int $userId, int $appId) {
    $apiPassword = 'u8JorEjFCcRDmbcdw5E7zIX7ViIb0u8j';
    
    $url = "https://api.gamebanana.com/Core/App/Authenticate?api_password={$apiPassword}&app_id={$appId}&userid={$userId}";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200 && $response !== false) {
        $token = trim($response);
        // Store token in session for future use with app-specific keys
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $cacheKey = 'gb_app_token_' . $appId;
        $userKey = 'gb_app_token_user_' . $appId;
        $timeKey = 'gb_app_token_time_' . $appId;
        
        $_SESSION[$cacheKey] = $token;
        $_SESSION[$userKey] = $userId;
        $_SESSION[$timeKey] = time();
        
        return $token;
    }
    
    return false;
}

// Get cached token if available and still valid
function vb_get_cached_token(int $userId, int $appId = 487): ?string {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Check if we have a valid cached token (valid for 12 hours)
    $cacheKey = 'gb_app_token_' . $appId;
    $userKey = 'gb_app_token_user_' . $appId;
    $timeKey = 'gb_app_token_time_' . $appId;
    
    if (isset($_SESSION[$cacheKey], $_SESSION[$userKey], $_SESSION[$timeKey]) &&
        $_SESSION[$userKey] === $userId &&
        (time() - $_SESSION[$timeKey]) < 43200) { // 12 hours validity
        return $_SESSION[$cacheKey];
    }
    
    return null;
}

// Require app authentication for protected endpoints
function vb_require_app_authentication(int $userId, int $appId): void {
    // First try to use cached token
    $cachedToken = vb_get_cached_token($userId, $appId);
    if ($cachedToken !== null) {
        return; // Token is valid and cached
    }
    
    // If no cached token, authenticate with API
    if (!vb_authenticate_app($userId, $appId)) {
        vb_json_response(['error' => 'App authentication failed'], 401);
    }
}

// IP-based rate limiting with file cache (15 requests per minute)
function vb_enforce_rate_limit(int $maxRequests = 15, int $timeWindow = 60): void {

    // TODO: add anti bot, abusive limits
    return; // Disable rate limiting for now
    // Skip rate limiting for OPTIONS preflight requests
    if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        return;
    }
    
    
    $cacheDir = __DIR__ . '/cache/rate_limit';
    
    // Ensure cache directory exists
    if (!is_dir($cacheDir)) {
        if (!mkdir($cacheDir, 0755, true) && !is_dir($cacheDir)) {
            error_log("Rate limit: Failed to create cache directory: $cacheDir");
            // Don't enforce rate limiting if cache dir can't be created
            return;
        }
    }
    
    // Check if directory is writable
    if (!is_writable($cacheDir)) {
        error_log("Rate limit: Cache directory not writable: $cacheDir");
        return;
    }
    
    // Get client IP address
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $ipHash = md5($ip);
    $cacheFile = $cacheDir . '/' . $ipHash . '.cache';
    
    $now = time();
    $data = ['count' => 0, 'window_start' => $now, 'last_request' => $now];
    
    // Read existing data if file exists
    if (file_exists($cacheFile)) {
        $fileData = file_get_contents($cacheFile);
        if ($fileData !== false) {
            $existingData = json_decode($fileData, true);
            if (is_array($existingData)) {
                $data = $existingData;
            }
        }
    }
    
    // Check if time window has expired
    if (($now - $data['window_start']) >= $timeWindow) {
        // Reset counter for new time window
        $data = ['count' => 1, 'window_start' => $now, 'last_request' => $now];
    } else {
        // Increment counter within current window
        $data['count']++;
        $data['last_request'] = $now;
    }
    
    // Enforce rate limit
    if ($data['count'] > $maxRequests) {
        echo "<!-- Mimicking GameBanana's module structure -->
<module id=\"RecentVisitors\" class=\"PageModule StrangeBerryModule\">
    <h2>
        <a href=\"https://gamebanana.com/apps/487\">Recent Visitors</a>
    </h2>
    <div class=\"Content\">
        <div class='Error'>Rate limit exceeded. Please try again later.</div>
    </div>
</module>";
        exit;
    }
    
    // Save updated data
    file_put_contents($cacheFile, json_encode($data), LOCK_EX);
    
    // Clean up old cache files (older than 2 hours)
    $cleanupTime = $now - 7200; // 2 hours
    $files = glob($cacheDir . '/*.cache');
    foreach ($files as $file) {
        if (filemtime($file) < $cleanupTime) {
            unlink($file);
        }
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Both Apps Together</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .app-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .app-title {
            color: #333;
            border-bottom: 2px solid #007cba;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007cba;
        }
        .conflict-test {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #ffc107;
        }
        .test-buttons {
            margin: 20px 0;
        }
        .test-btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-btn:hover {
            background: #005a87;
        }
        .console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>JavaScript Conflict Resolution Test</h1>
        
        <div class="test-info">
            <h3>Test Purpose</h3>
            <p>This page loads both the Visitors app and Trading Cards app together to verify that the JavaScript namespacing successfully resolves conflicts between the two apps.</p>
            <p><strong>Expected Result:</strong> Both apps should function independently without interfering with each other.</p>
        </div>

        <div class="conflict-test">
            <h3>Conflict Resolution Tests</h3>
            <div class="test-buttons">
                <button class="test-btn" onclick="testNamespaces()">Test Namespaces</button>
                <button class="test-btn" onclick="testGlobalFunctions()">Test Global Functions</button>
                <button class="test-btn" onclick="testVariableConflicts()">Test Variable Conflicts</button>
                <button class="test-btn" onclick="clearConsole()">Clear Console</button>
            </div>
            <div id="console-output" class="console-output">
                Console output will appear here...
            </div>
        </div>

        <div class="app-section">
            <h2 class="app-title">Visitors App</h2>
            <div id="visitors-app">
                <!-- Visitors app would be loaded here -->
                <p><em>Note: This is a test page. The actual apps would be loaded via PHP includes in a real GameBanana environment.</em></p>
                <p>Visitors app namespace: <code>window.VisitorsApp</code></p>
                <p>Global functions exposed: toggleSettingsPanel, saveSettings, toggleVisitHistory, closeVisitHistory</p>
            </div>
        </div>

        <div class="app-section">
            <h2 class="app-title">Trading Cards App</h2>
            <div id="trading-cards-app">
                <!-- Trading cards app would be loaded here -->
                <p><em>Note: This is a test page. The actual apps would be loaded via PHP includes in a real GameBanana environment.</em></p>
                <p>Trading Cards app namespace: <code>window.TradingCardsApp</code></p>
                <p>Global functions exposed: toggleTradingCardSettingsPanel, collectTradingCard, saveTradingCardSettings, collectTradingCardWithNotifications, redirectToTradingCardApp</p>
            </div>
        </div>
    </div>

    <script>
        // Test functions to verify namespace isolation
        function log(message) {
            const output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
        }

        function testNamespaces() {
            log('=== Testing Namespaces ===');
            
            // Test if namespaces exist
            if (typeof window.VisitorsApp !== 'undefined') {
                log('✓ VisitorsApp namespace exists');
            } else {
                log('✗ VisitorsApp namespace missing');
            }
            
            if (typeof window.TradingCardsApp !== 'undefined') {
                log('✓ TradingCardsApp namespace exists');
            } else {
                log('✗ TradingCardsApp namespace missing');
            }
            
            // Test namespace isolation
            if (window.VisitorsApp !== window.TradingCardsApp) {
                log('✓ Namespaces are properly isolated');
            } else {
                log('✗ Namespace conflict detected');
            }
        }

        function testGlobalFunctions() {
            log('=== Testing Global Function Exposure ===');
            
            // Test visitors app global functions
            const visitorsGlobals = ['toggleSettingsPanel', 'saveSettings', 'toggleVisitHistory', 'closeVisitHistory'];
            visitorsGlobals.forEach(func => {
                if (typeof window[func] === 'function') {
                    log(`✓ Visitors global function '${func}' is available`);
                } else {
                    log(`✗ Visitors global function '${func}' is missing`);
                }
            });
            
            // Test trading cards app global functions
            const tradingCardsGlobals = ['toggleTradingCardSettingsPanel', 'collectTradingCard', 'saveTradingCardSettings', 'collectTradingCardWithNotifications', 'redirectToTradingCardApp'];
            tradingCardsGlobals.forEach(func => {
                if (typeof window[func] === 'function') {
                    log(`✓ Trading Cards global function '${func}' is available`);
                } else {
                    log(`✗ Trading Cards global function '${func}' is missing`);
                }
            });
        }

        function testVariableConflicts() {
            log('=== Testing Variable Conflicts ===');
            
            // Test for common variable names that might conflict
            const potentialConflicts = ['baseUrl', 'currentStatsDays', 'tcSpawnCheckInterval', 'animateShow', 'animateHide', 'formatTime'];
            
            potentialConflicts.forEach(varName => {
                if (typeof window[varName] !== 'undefined') {
                    log(`⚠ Global variable '${varName}' exists - potential conflict`);
                } else {
                    log(`✓ No global variable '${varName}' - conflict avoided`);
                }
            });
            
            log('Note: Variables should be contained within their respective namespaces');
        }

        function clearConsole() {
            document.getElementById('console-output').innerHTML = 'Console cleared...<br>';
        }

        // Initialize test
        document.addEventListener('DOMContentLoaded', function() {
            log('Test page loaded successfully');
            log('Ready to test JavaScript conflict resolution');
        });
    </script>
</body>
</html>

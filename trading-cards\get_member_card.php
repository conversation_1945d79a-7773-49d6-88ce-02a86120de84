<?php
/**
 * AJAX endpoint for getting a specific member's card
 */

require_once 'common.php';
require_once 'db_config.php';
require_once 'card_system.php';

// Enable CORS for GameBanana
tc_enable_cors();

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Require allowed origin
tc_require_allowed_origin();

// Enforce rate limiting
vb_enforce_rate_limit(60, 300); // 60 requests per 5 minutes

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    tc_json_response(['error' => 'Method not allowed'], 405);
}

// Get parameters
$memberId = isset($_GET['member_id']) ? (int)$_GET['member_id'] : 0;

// Validate parameters
if ($memberId <= 0) {
    tc_json_response(['error' => 'Invalid member ID'], 400);
}

try {
    // Get or create the member's card
    $card = tc_getMemberCard($memberId);
    
    if ($card) {
        // Return the card data with cache headers (cache for 5 minutes)
        tc_json_response($card, 200, 300);
    } else {
        tc_json_response(['error' => 'Card not found'], 404);
    }
    
} catch (Exception $e) {
    error_log("Get member card error: " . $e->getMessage());
    tc_json_response(['error' => 'Failed to load card'], 500);
}
?>

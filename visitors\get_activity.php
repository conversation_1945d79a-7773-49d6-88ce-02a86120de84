<?php
// Return daily visit counts for a profile and optional visitor
// Params: profile_id (int, required), visitor_id (int, optional), days (int, optional, default 30)

date_default_timezone_set('UTC');

require_once __DIR__ . '/common.php';
vb_enable_cors_json(['https://gamebanana.com']);
header('Content-Type: application/json');

// Handle preflight
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Enforce rate limiting (15 requests per 5 minute)
vb_enforce_rate_limit(15, 300);

require_once 'db_config.php';

// Enforce allowed Origin/Referer
vb_require_allowed_origin(['https://gamebanana.com']);

// Authenticate app access if user is logged in
$profileId = isset($_GET['profile_id']) ? (int)$_GET['profile_id'] : 0;
if ($profileId > 0) {
    vb_require_app_authentication($profileId, 487);
}

$visitorId = isset($_GET['_idMember']) ? (int)$_GET['_idMember'] : null;
$days = isset($_GET['days']) ? (int)$_GET['days'] : 30;
if ($days < 1 || $days > 365) { $days = 30; }

if ($profileId <= 0) {
    vb_json_response(['error' => 'Invalid profile ID'], 400);
}

try {
    global $db;

    // Build SQL
    $sinceExpr = "DATE_SUB(UTC_TIMESTAMP(), INTERVAL ? DAY)";
    if ($visitorId && $visitorId > 0) {
        $sql = "SELECT DATE(visit_time) AS d, COUNT(*) AS c
                FROM visit_history
                WHERE profile_id = ? AND visitor_id = ? AND visit_time >= $sinceExpr
                GROUP BY d ORDER BY d ASC";
        $stmt = $db->prepare($sql);
        if (!$stmt) { throw new Exception('Prepare failed: ' . $db->error); }
        $stmt->bind_param('iii', $profileId, $visitorId, $days);
    } else {
        $sql = "SELECT DATE(visit_time) AS d, COUNT(*) AS c
                FROM visit_history
                WHERE profile_id = ? AND visit_time >= $sinceExpr
                GROUP BY d ORDER BY d ASC";
        $stmt = $db->prepare($sql);
        if (!$stmt) { throw new Exception('Prepare failed: ' . $db->error); }
        $stmt->bind_param('ii', $profileId, $days);
    }

    $stmt->execute();
    $res = $stmt->get_result();
    $byDate = [];
    while ($row = $res->fetch_assoc()) {
        $byDate[$row['d']] = (int)$row['c'];
    }
    $stmt->close();

    // Build contiguous series for the last N days ending today (UTC)
    $counts = [];
    $dates = [];
    $max = 0;
    $today = new DateTime('now', new DateTimeZone('UTC'));
    $start = clone $today;
    $start->modify('-' . ($days - 1) . ' days');

    $cursor = clone $start;
    while ($cursor <= $today) {
        $key = $cursor->format('Y-m-d');
        $val = isset($byDate[$key]) ? $byDate[$key] : 0;
        $counts[] = $val;
        $dates[] = $key;
        if ($val > $max) { $max = $val; }
        $cursor->modify('+1 day');
    }

    vb_json_response([
        'profile_id' => $profileId,
        'visitor_id' => $visitorId,
        'days' => $days,
        'start_date' => $start->format('Y-m-d'),
        'end_date' => $today->format('Y-m-d'),
        'dates' => $dates,
        'counts' => $counts,
        'max' => $max
    ], 200, 120);
} catch (Exception $e) {
    error_log('Activity error: ' . $e->getMessage());
    vb_json_response(['error' => 'Server error'], 500);
}

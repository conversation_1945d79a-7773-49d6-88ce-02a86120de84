<?php
/**
 * Spawn Manager - <PERSON><PERSON> job script to automatically manage card spawns
 * Run this script periodically (every 5-10 minutes) to update spawn statuses
 */

require_once 'common.php';
require_once 'db_config.php';
require_once 'card_system.php';

// Simple protection - only run from command line or with admin key
if (php_sapi_name() !== 'cli' && ($_GET['admin_key'] ?? '') !== 'tc_admin_2024') {
    die('Access denied');
}

echo "Trading Cards Spawn Manager\n";
echo "==========================\n";
echo "Started at: " . date('Y-m-d H:i:s') . "\n\n";

global $db;

// Get all cards that should be spawned but aren't
$query = "SELECT member_id, last_spawn_time, spawn_rate_minutes, rarity 
          FROM member_cards 
          WHERE is_spawned = 0";

$result = $db->query($query);
$spawned = 0;
$checked = 0;

while ($row = $result->fetch_assoc()) {
    $checked++;
    $memberId = $row['member_id'];
    
    if (tc_should_spawn_card($row['last_spawn_time'], $row['spawn_rate_minutes'])) {
        // Spawn the card
        $updateQuery = "UPDATE member_cards SET is_spawned = 1, last_spawn_time = UTC_TIMESTAMP() WHERE member_id = ?";
        $updateStmt = $db->prepare($updateQuery);
        $updateStmt->bind_param('i', $memberId);
        
        if ($updateStmt->execute()) {
            tc_logSpawnEvent($memberId);
            $spawned++;
            echo "Spawned {$row['rarity']} card for member $memberId\n";
        }
    }
}

// Clean up old spawn logs (older than 30 days)
$cleanupQuery = "DELETE FROM card_spawn_log WHERE spawned_at < DATE_SUB(UTC_TIMESTAMP(), INTERVAL 30 DAY)";
$cleanupResult = $db->query($cleanupQuery);
$cleanedLogs = $db->affected_rows;

// Get current statistics
$statsQuery = "SELECT 
    COUNT(*) as total_cards,
    SUM(CASE WHEN is_spawned = 1 THEN 1 ELSE 0 END) as spawned_count,
    SUM(CASE WHEN rarity = 'common' AND is_spawned = 1 THEN 1 ELSE 0 END) as spawned_common,
    SUM(CASE WHEN rarity = 'uncommon' AND is_spawned = 1 THEN 1 ELSE 0 END) as spawned_uncommon,
    SUM(CASE WHEN rarity = 'rare' AND is_spawned = 1 THEN 1 ELSE 0 END) as spawned_rare,
    SUM(CASE WHEN rarity = 'epic' AND is_spawned = 1 THEN 1 ELSE 0 END) as spawned_epic,
    SUM(CASE WHEN rarity = 'legendary' AND is_spawned = 1 THEN 1 ELSE 0 END) as spawned_legendary
FROM member_cards";

$statsResult = $db->query($statsQuery);
$stats = $statsResult->fetch_assoc();

echo "\nSpawn Manager Results:\n";
echo "- Checked: $checked cards\n";
echo "- Spawned: $spawned cards\n";
echo "- Cleaned: $cleanedLogs old logs\n";
echo "\nCurrent Status:\n";
echo "- Total cards: {$stats['total_cards']}\n";
echo "- Currently spawned: {$stats['spawned_count']}\n";
echo "  - Common: {$stats['spawned_common']}\n";
echo "  - Uncommon: {$stats['spawned_uncommon']}\n";
echo "  - Rare: {$stats['spawned_rare']}\n";
echo "  - Epic: {$stats['spawned_epic']}\n";
echo "  - Legendary: {$stats['spawned_legendary']}\n";

echo "\nCompleted at: " . date('Y-m-d H:i:s') . "\n";

// If running from web, show HTML output
if (php_sapi_name() !== 'cli') {
    echo "<br><a href='admin.php?admin_key=tc_admin_2024'>Back to Admin Panel</a>";
}
?>

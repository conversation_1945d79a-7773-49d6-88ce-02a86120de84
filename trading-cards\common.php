<?php
// Common utilities for Trading Cards app
// Set default timezone consistently
if (!ini_get('date.timezone')) {
    date_default_timezone_set('UTC');
}

// Include the visitors common functions for shared utilities
require_once __DIR__ . '/../visitors/common.php';

// Trading card specific constants
define('TC_CACHE_DIR', __DIR__ . '/cache/');
define('TC_CACHE_DURATION', 3600); // 1 hour cache for card data

// Ensure cache directory exists
if (!is_dir(TC_CACHE_DIR)) {
    mkdir(TC_CACHE_DIR, 0755, true);
}

// Card rarity definitions with spawn rates (in minutes)
const CARD_RARITIES = [
    'common' => [
        'weight' => 60,
        'min_spawn_minutes' => 30,
        'max_spawn_minutes' => 60,
        'color' => '#9e9e9e',
        'border_color' => '#757575',
        'name' => 'Member'
    ],
    'uncommon' => [
        'weight' => 25,
        'min_spawn_minutes' => 120,
        'max_spawn_minutes' => 240,
        'color' => '#4caf50',
        'border_color' => '#2e7d32',
        'name' => 'Game Manager'
    ],
    'rare' => [
        'weight' => 10,
        'min_spawn_minutes' => 360,
        'max_spawn_minutes' => 720,
        'color' => '#2196f3',
        'border_color' => '#1565c0',
        'name' => 'Moderator'
    ],
    'epic' => [
        'weight' => 4,
        'min_spawn_minutes' => 1440,
        'max_spawn_minutes' => 2880,
        'color' => '#9c27b0',
        'border_color' => '#6a1b9a',
        'name' => 'Admin'
    ],
    'legendary' => [
        'weight' => 1,
        'min_spawn_minutes' => 4320,
        'max_spawn_minutes' => 10080,
        'color' => '#ff9800',
        'border_color' => '#e65100',
        'name' => 'Super Admin'
    ]
];

/**
 * Generate a random rarity based on weights
 */
function tc_generate_random_rarity() {
    $totalWeight = array_sum(array_column(CARD_RARITIES, 'weight'));
    $random = mt_rand(1, $totalWeight);
    
    $currentWeight = 0;
    foreach (CARD_RARITIES as $rarity => $config) {
        $currentWeight += $config['weight'];
        if ($random <= $currentWeight) {
            return $rarity;
        }
    }
    
    return 'common'; // Fallback
}

/**
 * Generate spawn rate for a given rarity
 */
function tc_generate_spawn_rate($rarity) {
    if (!isset(CARD_RARITIES[$rarity])) {
        return 60; // Default 1 hour
    }
    
    $config = CARD_RARITIES[$rarity];
    return mt_rand($config['min_spawn_minutes'], $config['max_spawn_minutes']);
}

/**
 * Get rarity configuration
 */
function tc_get_rarity_config($rarity) {
    return CARD_RARITIES[$rarity] ?? CARD_RARITIES['common'];
}

/**
 * Format time remaining until next spawn
 */
function tc_format_time_remaining($timestamp) {
    $diff = strtotime($timestamp) - time();
    
    if ($diff <= 0) {
        return 'Available now';
    }
    
    if ($diff < 3600) {
        $minutes = ceil($diff / 60);
        return $minutes . 'm remaining';
    } elseif ($diff < 86400) {
        $hours = ceil($diff / 3600);
        return $hours . 'h remaining';
    } else {
        $days = ceil($diff / 86400);
        return $days . 'd remaining';
    }
}

/**
 * Check if a card should be spawned based on its last spawn time and spawn rate
 */
function tc_should_spawn_card($lastSpawnTime, $spawnRateMinutes) {
    if (!$lastSpawnTime) {
        return true; // Never spawned before
    }
    
    $nextSpawnTime = strtotime($lastSpawnTime) + ($spawnRateMinutes * 60);
    return time() >= $nextSpawnTime;
}

/**
 * Get next spawn time for a card
 */
function tc_get_next_spawn_time($lastSpawnTime, $spawnRateMinutes) {
    if (!$lastSpawnTime) {
        return time(); // Available now
    }
    
    return strtotime($lastSpawnTime) + ($spawnRateMinutes * 60);
}

/**
 * JSON response helper specifically for trading cards
 */
function tc_json_response($data, int $status = 200, ?int $cacheSeconds = null): void {
    vb_json_response($data, $status, $cacheSeconds);
}

/**
 * Require origin check for trading cards API
 */
function tc_require_allowed_origin(): void {
    vb_require_allowed_origin(['https://gamebanana.com']);
}

/**
 * Enable CORS for trading cards API
 */
function tc_enable_cors(): void {
    vb_enable_cors_json(['https://gamebanana.com']);
}
?>

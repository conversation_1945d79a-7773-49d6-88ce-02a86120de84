/* Trading Cards App Styles - GameBanana Compatible */

/* Base module styling matching GameBanana */

#TradingCards .Tabs {
    display: flex;
    gap: 0px !important;
    margin: 6px 0 10px;
}

#TradingCards .Tab {
    padding: 4px 8px;
    border-radius: 4px;
    text-decoration: none;
    color: inherit;
    background: rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

#TradingCards .TabActive {
    background: rgba(255, 255, 255, 0.15);
    font-weight: bold;
}

/* Panel transitions */
#TradingCards .PanelTransition {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 180ms ease, transform 200ms ease;
}

#TradingCards .PanelTransition[style*="display: none"] {
    opacity: 0;
    transform: translateY(6px);
}

/* Trading Card specific styles - TCG Style with Enhanced Texture */
#TradingCards .TradingCard {
    position: relative;
    border-radius: 12px;
    margin: 10px auto;
    border: 4px solid;
    transition: all 0.3s ease;
    overflow: hidden;
    cursor: pointer;
    background:
        /* More visible diagonal stripes */
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 15px,
            rgba(255,255,255,0.03) 15px,
            rgba(255,255,255,0.03) 30px
        ),
        /* Enhanced noise texture */
        radial-gradient(circle at 2px 2px, rgba(255,255,255,0.02) 1px, transparent 0),
        /* Base dark background */
        #1a1a1a;
    background-size: auto, 22px 22px, auto;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255,255,255,0.1),
        inset 0 -1px 0 rgba(0,0,0,0.2);
    display: flex;
    flex-direction: column;
}

/* Card Size Variations */
#TradingCards .TradingCard.card-size-small {
    width: 200px;
    height: 270px;
}

#TradingCards .TradingCard.card-size-medium {
    width: 280px;
    height: 380px;
}

#TradingCards .TradingCard.card-size-large {
    width: 350px;
    height: 475px;
}

/* Responsive card sizing */
@media (max-width: 768px) {
    #TradingCards .TradingCard.card-size-large {
        width: 280px;
        height: 380px;
    }
    #TradingCards .TradingCard.card-size-medium {
        width: 240px;
        height: 325px;
    }
    #TradingCards .TradingCard.card-size-small {
        width: 180px;
        height: 245px;
    }
}

@media (max-width: 480px) {
    #TradingCards .TradingCard.card-size-large,
    #TradingCards .TradingCard.card-size-medium {
        width: 220px;
        height: 300px;
    }
    #TradingCards .TradingCard.card-size-small {
        width: 160px;
        height: 220px;
    }
}

/* Subtle texture overlay */
#TradingCards .TradingCard::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        repeating-linear-gradient(
            90deg,
            transparent,
            transparent 3px,
            rgba(255,255,255,0.01) 3px,
            rgba(255,255,255,0.01) 6px
        );
    opacity: 0.5;
    transition: opacity 0.3s ease;
    z-index: 1;
}

/* Very subtle shine effect on hover */
#TradingCards .TradingCard::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        45deg,
        transparent 40%,
        rgba(255,255,255,0.03) 48%,
        rgba(255,255,255,0.06) 50%,
        rgba(255,255,255,0.03) 52%,
        transparent 60%
    );
    opacity: 0;
    transform: rotate(45deg);
    transition: all 0.8s ease;
    z-index: 2;
    pointer-events: none;
}

#TradingCards .TradingCard:hover::before {
    opacity: 0.7;
}

#TradingCards .TradingCard:hover::after {
    opacity: 1;
    animation: subtleSweep 2s ease-in-out;
}

#TradingCards .TradingCard:hover {
    transform: translateY(-2px) scale(1.01);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* Enhanced clickable card styling */
#TradingCards .TradingCard[onclick] {
    cursor: pointer;
    transition: all 0.3s ease, transform 0.2s ease;
    position: relative;
    animation: collectiblePulse 3s ease-in-out infinite;
}

/* Special styling for cards that redirect to app */
#TradingCards .TradingCard[onclick*="redirectToTradingCardApp"] {
    animation: appRedirectPulse 3s ease-in-out infinite;
}

#TradingCards .TradingCard[onclick*="redirectToTradingCardApp"]:hover {
    transform: translateY(-4px) scale(1.03);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.5),
        0 0 30px rgba(59, 130, 246, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

@keyframes appRedirectPulse {
    0%, 100% {
        box-shadow:
            0 8px 25px rgba(0, 0, 0, 0.4),
            inset 0 1px 0 rgba(255,255,255,0.1),
            inset 0 -1px 0 rgba(0,0,0,0.2),
            0 0 0 1px rgba(59, 130, 246, 0.2);
    }
    50% {
        box-shadow:
            0 8px 25px rgba(0, 0, 0, 0.4),
            inset 0 1px 0 rgba(255,255,255,0.1),
            inset 0 -1px 0 rgba(0,0,0,0.2),
            0 0 0 2px rgba(59, 130, 246, 0.4);
    }
}

#TradingCards .TradingCard[onclick]:hover {
    transform: translateY(-4px) scale(1.03);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.5),
        0 0 30px rgba(16, 185, 129, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

#TradingCards .TradingCard[onclick]:active {
    transform: translateY(-1px) scale(1.01);
    transition: all 0.1s ease;
}

/* Green scan effect for collectible cards */
#TradingCards .TradingCard[onclick]::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        45deg,
        transparent 40%,
        rgba(16, 185, 129, 0.3) 48%,
        rgba(52, 211, 153, 0.6) 50%,
        rgba(16, 185, 129, 0.3) 52%,
        transparent 60%
    );
    opacity: 0;
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    transition: none;
    z-index: 3;
    pointer-events: none;
}

#TradingCards .TradingCard[onclick]:hover::before {
    opacity: 1;
    animation: greenScan 1.5s ease-out;
}

@keyframes greenScan {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
        opacity: 0;
    }
}

/* Subtle border glow for collectible indication */
#TradingCards .TradingCard[onclick] {
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255,255,255,0.1),
        inset 0 -1px 0 rgba(0,0,0,0.2),
        0 0 0 1px rgba(16, 185, 129, 0.2);
}

@keyframes collectiblePulse {
    0%, 100% {
        box-shadow:
            0 8px 25px rgba(0, 0, 0, 0.4),
            inset 0 1px 0 rgba(255,255,255,0.1),
            inset 0 -1px 0 rgba(0,0,0,0.2),
            0 0 0 1px rgba(16, 185, 129, 0.2);
    }
    50% {
        box-shadow:
            0 8px 25px rgba(0, 0, 0, 0.4),
            inset 0 1px 0 rgba(255,255,255,0.1),
            inset 0 -1px 0 rgba(0,0,0,0.2),
            0 0 0 2px rgba(16, 185, 129, 0.4);
    }
}

/* Collection states */
#TradingCards .TradingCard.collecting {
    cursor: wait !important;
    opacity: 0.8;
}

#TradingCards .TradingCard.collected {
    animation: collectSuccess 0.6s ease-out;
}

@keyframes collectSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1.05); }
}

/* Disable hover effects during collection */
#TradingCards .TradingCard.collecting:hover {
    transform: translateY(-2px) scale(1.01) !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3) !important;
}

#TradingCards .TradingCard.collecting::after {
    display: none !important;
}

@keyframes subtleSweep {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }
}

/* Rarity colors - Enhanced TCG Style with More Visible Texture */
#TradingCards .TradingCard.common {
    border-color: #6b7280;
    background:
        /* More visible diagonal stripes matching module background */
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 15px,
            rgba(255,255,255,0.03) 15px,
            rgba(255,255,255,0.03) 30px
        ),
        /* Enhanced noise texture */
        radial-gradient(circle at 2px 2px, rgba(255,255,255,0.02) 1px, transparent 0),
        /* Rarity-specific gradient */
        linear-gradient(145deg, #374151 0%, #1f2937 100%);
    background-size: auto, 22px 22px, auto;
}

#TradingCards .TradingCard.uncommon {
    border-color: #10b981;
    background:
        /* More visible diagonal stripes matching module background */
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 15px,
            rgba(255,255,255,0.03) 15px,
            rgba(255,255,255,0.03) 30px
        ),
        /* Enhanced noise texture */
        radial-gradient(circle at 2px 2px, rgba(255,255,255,0.02) 1px, transparent 0),
        /* Rarity-specific gradient */
        linear-gradient(145deg, #065f46 0%, #064e3b 100%);
    background-size: auto, 22px 22px, auto;
}

#TradingCards .TradingCard.rare {
    border-color: #3b82f6;
    background:
        /* More visible diagonal stripes matching module background */
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 15px,
            rgba(255,255,255,0.03) 15px,
            rgba(255,255,255,0.03) 30px
        ),
        /* Enhanced noise texture */
        radial-gradient(circle at 2px 2px, rgba(255,255,255,0.02) 1px, transparent 0),
        /* Rarity-specific gradient */
        linear-gradient(145deg, #1e40af 0%, #1e3a8a 100%);
    background-size: auto, 22px 22px, auto;
}

#TradingCards .TradingCard.epic {
    border-color: #8b5cf6;
    background:
        /* More visible diagonal stripes matching module background */
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 15px,
            rgba(255,255,255,0.03) 15px,
            rgba(255,255,255,0.03) 30px
        ),
        /* Enhanced noise texture */
        radial-gradient(circle at 2px 2px, rgba(255,255,255,0.02) 1px, transparent 0),
        /* Rarity-specific gradient */
        linear-gradient(145deg, #7c3aed 0%, #6d28d9 100%);
    background-size: auto, 22px 22px, auto;
}

#TradingCards .TradingCard.legendary {
    border-color: #f59e0b;
    background:
        /* More visible diagonal stripes matching module background */
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 15px,
            rgba(255,255,255,0.035) 15px,
            rgba(255,255,255,0.035) 30px
        ),
        /* Enhanced noise texture */
        radial-gradient(circle at 2px 2px, rgba(255,255,255,0.025) 1px, transparent 0),
        /* Subtle legendary accent */
        radial-gradient(circle at 50% 50%, rgba(217,119,6,0.1) 0%, transparent 70%),
        /* Rarity-specific gradient */
        linear-gradient(145deg, #d97706 0%, #b45309 100%);
    background-size: auto, 22px 22px, auto, auto;
    box-shadow:
        0 8px 25px rgba(245, 158, 11, 0.2),
        inset 0 1px 0 rgba(255,255,255,0.1);
}

#TradingCards .TradingCard.legendary:hover {
    box-shadow:
        0 8px 20px rgba(245, 158, 11, 0.3),
        inset 0 1px 0 rgba(255,255,255,0.15);
}

/* Card content layout - TCG Style with Texture */
#TradingCards .CardContent {
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
    z-index: 3;
    padding: 15px;
    background:
        radial-gradient(circle at 50% 0%, rgba(255,255,255,0.05) 0%, transparent 50%),
        linear-gradient(180deg, rgba(0,0,0,0.1) 0%, transparent 30%, transparent 70%, rgba(0,0,0,0.1) 100%);
}

/* TCG Card Header */
#TradingCards .CardHeader {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

#TradingCards .CardName {
    font-weight: bold;
    font-size: 18px;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    margin: 0;
}

#TradingCards .CardName a {
    color: inherit;
    text-decoration: none;
}

#TradingCards .CardName a:hover {
    text-decoration: underline;
}

#TradingCards .CardLevel {
    background: rgba(0, 0, 0, 0.6);
    color: #ffffff;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: bold;
    border: 1px solid rgba(255, 255, 255, 0.3);
    min-width: 24px;
    text-align: center;
}

#TradingCards .CardLevel.new-card {
    background: linear-gradient(45deg, #10b981, #34d399);
    color: #ffffff;
    font-size: 10px;
    padding: 3px 6px;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 6px rgba(16, 185, 129, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.4);
}

/* TCG Card Avatar Section - Responsive */
#TradingCards .CardAvatar {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
    position: relative;
}

#TradingCards .CardAvatar img {
    width: 120px;
    height: 120px;
    border-radius: 12px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255,255,255,0.1);
    object-fit: cover;
    position: relative;
    z-index: 2;
}

#TradingCards .CardAvatar img:hover {
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.02);
    box-shadow:
        0 6px 16px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255,255,255,0.15);
}

/* Avatar sizing based on card size */
#TradingCards .TradingCard.card-size-small .CardAvatar img {
    width: 80px;
    height: 80px;
}

#TradingCards .TradingCard.card-size-large .CardAvatar img {
    width: 150px;
    height: 150px;
}

/* Font sizing based on card size */
#TradingCards .TradingCard.card-size-small .CardName {
    font-size: 14px;
}

#TradingCards .TradingCard.card-size-small .CardRarity {
    font-size: 11px;
    padding: 4px 8px;
}

#TradingCards .TradingCard.card-size-small .CardLevel {
    font-size: 11px;
    padding: 2px 6px;
}

#TradingCards .TradingCard.card-size-small .CardLevel.new-card {
    font-size: 8px;
    padding: 2px 4px;
}

#TradingCards .TradingCard.card-size-large .CardName {
    font-size: 22px;
}

#TradingCards .TradingCard.card-size-large .CardRarity {
    font-size: 16px;
    padding: 8px 16px;
}

#TradingCards .TradingCard.card-size-large .CardLevel {
    font-size: 16px;
    padding: 6px 12px;
}

#TradingCards .TradingCard.card-size-large .CardLevel.new-card {
    font-size: 12px;
    padding: 4px 8px;
}

/* TCG Card Info Section */
#TradingCards .CardInfo {
    margin-top: auto;
}

#TradingCards .CardRarity {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    text-transform: uppercase;
    margin-bottom: 10px;
    text-align: center;
    width: 100%;
    box-sizing: border-box;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

#TradingCards .CardRarity.common {
    background: linear-gradient(45deg, #6b7280, #9ca3af);
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}
#TradingCards .CardRarity.uncommon {
    background: linear-gradient(45deg, #10b981, #34d399);
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}
#TradingCards .CardRarity.rare {
    background: linear-gradient(45deg, #3b82f6, #60a5fa);
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}
#TradingCards .CardRarity.epic {
    background: linear-gradient(45deg, #8b5cf6, #a78bfa);
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}
#TradingCards .CardRarity.legendary {
    background: linear-gradient(45deg, #f59e0b, #fbbf24);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    box-shadow:
        0 2px 6px rgba(0,0,0,0.2),
        0 0 8px rgba(245,158,11,0.2);
}

#TradingCards .CardStatus {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
    margin-bottom: 10px;
}

#TradingCards .CardStats {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
    margin-bottom: 15px;
}

#TradingCards .CardActions {
    display: flex;
    justify-content: center;
    margin-top: auto;
}

#TradingCards .CollectButton {
    padding: 10px 20px;
    border-radius: 25px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    background: linear-gradient(45deg, #10b981, #34d399);
    color: white;
    font-weight: bold;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

#TradingCards .CollectButton:hover {
    background: linear-gradient(45deg, #059669, #10b981);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.6);
    border-color: rgba(255, 255, 255, 0.5);
}

#TradingCards .CollectButton:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
}

#TradingCards .CollectButton:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Collection grid - TCG Style */
#TradingCards .CollectionGrid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    margin-top: 15px;
    padding: 20px;
}

#TradingCards .CollectionCard {
    background: rgba(0,0,0,0.1);
    border-radius: 6px;
    padding: 10px;
    border: 1px solid rgba(255,255,255,0.1);
    transition: all 0.2s ease;
}

#TradingCards .CollectionCard:hover {
    background: rgba(0,0,0,0.15);
    transform: translateY(-1px);
}

/* Stats panel */
#TradingCards .StatsPanel {
    margin-top: 8px;
    padding: 12px;
    background: rgba(0,0,0,0.1);
    border-radius: 6px;
    border: 1px solid rgba(255,255,255,0.1);
}

#TradingCards .StatsGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
}

#TradingCards .StatItem {
    text-align: center;
    padding: 8px;
    background: rgba(0,0,0,0.1);
    border-radius: 4px;
}

#TradingCards .StatValue {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 4px;
}

#TradingCards .StatLabel {
    font-size: 12px;
    opacity: 0.8;
}

/* Loading states */
#TradingCards .Loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    font-size: 14px;
    opacity: 0.7;
}

#TradingCards .Empty {
    text-align: center;
    padding: 20px;
    opacity: 0.7;
    font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
    #TradingCards .CollectionGrid {
        grid-template-columns: 1fr;
    }
    
    #TradingCards .CardContent {
        flex-direction: column;
        text-align: center;
    }
    
    #TradingCards .CardActions {
        align-items: center;
        flex-direction: row;
    }
}

/* Animation for card collection */
@keyframes cardCollected {
    0% { transform: scale(1); opacity: 1; }
    25% { transform: scale(1.05) rotate(2deg); opacity: 0.9; }
    50% { transform: scale(1.1) rotate(-1deg); opacity: 0.8; }
    75% { transform: scale(1.05) rotate(1deg); opacity: 0.7; }
    100% { transform: scale(0.95); opacity: 0.4; }
}

#TradingCards .TradingCard.collected {
    animation: cardCollected 0.8s ease-out;
}

/* Shimmer effect for legendary cards */
@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

#TradingCards .TradingCard.legendary::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    background-size: 200% 100%;
    animation: shimmer 3s infinite;
    border-radius: 8px;
    pointer-events: none;
}



/* Loading spinner */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#TradingCards .Loading img {
    animation: spin 1s linear infinite;
}

/* Hover effects for collection cards */
#TradingCards .CollectionCard:hover .TradingCard {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Tooltip styles */
#TradingCards .tooltip {
    position: relative;
    cursor: help;
}

#TradingCards .tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

#TradingCards .tooltip:hover::after {
    opacity: 1;
}

/* Card glow effects based on rarity */
#TradingCards .TradingCard.rare:hover {
    box-shadow: 0 8px 16px rgba(33,150,243,0.3), 0 0 20px rgba(33,150,243,0.2);
}

#TradingCards .TradingCard.epic:hover {
    box-shadow: 0 8px 16px rgba(156,39,176,0.3), 0 0 20px rgba(156,39,176,0.2);
}

#TradingCards .TradingCard.legendary:hover {
    box-shadow: 0 8px 16px rgba(255,152,0,0.4), 0 0 25px rgba(255,152,0,0.3);
}

/* Improved button states */
#TradingCards .CollectButton:active {
    transform: scale(0.95);
}

#TradingCards .CollectButton.collecting {
    background: #ff9800 !important;
    cursor: wait;
}

/* Better empty state styling */
#TradingCards .Empty {
    background: rgba(0,0,0,0.05);
    border: 2px dashed rgba(255,255,255,0.2);
    border-radius: 8px;
    padding: 30px 20px;
    margin: 20px 0;
}

/* Notification styles (for JavaScript notifications) */
.tc-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-weight: bold;
    z-index: 10000;
    transition: all 0.3s ease;
    transform: translateX(100%);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.tc-notification.success {
    background: linear-gradient(135deg, #4caf50, #45a049);
}

.tc-notification.error {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

.tc-notification.show {
    transform: translateX(0);
}

/* Settings panel styles (similar to visitors app) */
#TradingCards .ModeSwitcher {
    float: right;
    margin-right: 10px;
    font-size: 16px;
    text-decoration: none;
}

#TradingCards .ModeSwitcher .toggle-icon {
    margin-left: 6px;
    font-size: 12px;
    opacity: 0.85;
    vertical-align: middle;
}

#TradingCards .SettingsPanel {
    margin-bottom: 15px;
    padding: 10px;
    background-color: rgba(var(--PageModuleContentBackgroundColor), 0.8);
    border: 1px solid rgba(var(--BorderColor), 0.2);
    border-radius: 4px;
}

#TradingCards .SettingsPanel h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
}

#TradingCards .SettingRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

#TradingCards .SettingRow label {
    font-weight: bold;
}

#TradingCards .SettingRow select {
    padding: 4px;
    border-radius: 3px;
    border: 1px solid rgba(var(--BorderColor), 0.3);
    background-color: rgba(var(--PageBackgroundColor), 0.8);
    color: rgba(var(--TextColor), 1);
}

#TradingCards .SaveFeedback {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px 0;
    padding: 5px;
    background-color: rgba(var(--PageBackgroundColor), 0.5);
    border-radius: 3px;
}

#TradingCards .feedbackText {
    font-size: 12px;
}

#TradingCards .SettingActions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 12px;
}

#TradingCards .SettingActions button {
    padding: 4px 10px;
    border-radius: 3px;
    border: 1px solid rgba(var(--BorderColor), 0.3);
    background-color: rgba(var(--PageBackgroundColor), 0.8);
    color: rgba(var(--TextColor), 1);
    cursor: pointer;
}

#TradingCards .SettingActions button:hover {
    background-color: rgba(var(--PageBackgroundColor), 1);
}

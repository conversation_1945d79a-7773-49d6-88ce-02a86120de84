<?php
/**
 * AJAX endpoint for getting member's card collection
 */

require_once 'common.php';
require_once 'db_config.php';
require_once 'card_system.php';

// Enable CORS for GameBanana
tc_enable_cors();

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Require allowed origin
tc_require_allowed_origin();

// Enforce rate limiting
vb_enforce_rate_limit(60, 300); // 60 requests per 5 minutes

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    tc_json_response(['error' => 'Method not allowed'], 405);
}

// Get parameters
$memberId = isset($_GET['member_id']) ? (int)$_GET['member_id'] : 0;
$limit = isset($_GET['limit']) ? min(100, max(1, (int)$_GET['limit'])) : 50;
$offset = isset($_GET['offset']) ? max(0, (int)$_GET['offset']) : 0;

// Validate parameters
if ($memberId <= 0) {
    tc_json_response(['error' => 'Invalid member ID'], 400);
}

// Authenticate the member
vb_require_app_authentication($memberId, 1080);

try {
    // Get collection and stats
    $collection = tc_getMemberCollection($memberId, $limit, $offset);
    $stats = tc_getCollectionStats($memberId);
    
    // Return the data
    tc_json_response([
        'collection' => $collection,
        'stats' => $stats,
        'pagination' => [
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => count($collection) === $limit
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Get collection error: " . $e->getMessage());
    tc_json_response(['error' => 'Failed to load collection'], 500);
}
?>

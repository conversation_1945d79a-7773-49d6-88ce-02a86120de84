-- Trading Cards Database Schema
-- Run this SQL to set up the trading card system tables

-- Table to store each member's unique trading card
CREATE TABLE IF NOT EXISTS `member_cards` (
  `member_id` int(11) NOT NULL,
  `rarity` enum('common','uncommon','rare','epic','legendary') NOT NULL DEFAULT 'common',
  `spawn_rate_minutes` int(11) NOT NULL,
  `last_spawn_time` timestamp NULL DEFAULT NULL,
  `is_spawned` tinyint(1) NOT NULL DEFAULT 0,
  `total_collections` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`member_id`),
  KEY `idx_spawned` (`is_spawned`),
  KEY `idx_rarity` (`rarity`),
  KEY `idx_last_spawn` (`last_spawn_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table to track who collected which cards
CREATE TABLE IF NOT EXISTS `card_collections` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `collector_id` int(11) NOT NULL,
  `card_owner_id` int(11) NOT NULL,
  `collected_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `rarity_at_collection` enum('common','uncommon','rare','epic','legendary') NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_collection` (`collector_id`, `card_owner_id`),
  KEY `idx_collector` (`collector_id`),
  KEY `idx_card_owner` (`card_owner_id`),
  KEY `idx_collected_at` (`collected_at`),
  KEY `idx_rarity` (`rarity_at_collection`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table to track spawn events for analytics and debugging
CREATE TABLE IF NOT EXISTS `card_spawn_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL,
  `spawned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `collected_by` int(11) DEFAULT NULL,
  `collected_at` timestamp NULL DEFAULT NULL,
  `expired_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_member` (`member_id`),
  KEY `idx_spawned_at` (`spawned_at`),
  KEY `idx_collected_by` (`collected_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table to store trading card app settings per profile
CREATE TABLE IF NOT EXISTS `trading_card_settings` (
  `profile_id` int(11) NOT NULL,
  `settings` text NOT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`profile_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert some sample data for testing (optional)
-- This will create cards for some common GameBanana member IDs
INSERT IGNORE INTO `member_cards` (`member_id`, `rarity`, `spawn_rate_minutes`, `is_spawned`) VALUES
(1, 'legendary', 4320, 1),  -- GameBanana admin - legendary card
(123456, 'rare', 480, 0),
(234567, 'uncommon', 180, 1),
(345678, 'common', 45, 1),
(456789, 'epic', 1440, 0);

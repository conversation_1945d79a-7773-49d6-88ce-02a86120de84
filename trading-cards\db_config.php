<?php
// Database configuration for Trading Cards app
// Uses the same database as the visitors app

$host = 'kurisu.x10.mx';
$dbname = 'kurisuxm_gamebanana';
$username = 'kurisuxm_kurisuxm';
$password = '2wRGqwmSwjgz';

// Create a mysqli connection
try {
    $db = new mysqli($host, $username, $password, $dbname);
    
    // Check connection
    if ($db->connect_error) {
        throw new Exception("Connection failed: " . $db->connect_error);
    }
    
    // Set MySQL session timezone to UTC to match PHP timezone
    $db->query("SET time_zone = '+00:00'");
    
} catch(Exception $e) {
    // Log the error instead of displaying it to users
    error_log("Trading Cards Database Connection Error: " . $e->getMessage());
    // Return a generic error message
    die("We're experiencing technical difficulties. Please try again later.");
}
?>

<?php
// Set the default timezone
date_default_timezone_set('UTC');

require_once 'db_config.php';

/**
 * Records a new visitor to a profile
 * 
 * @param int $profileId The ID of the profile being visited
 * @param int $visitorId The ID of the visitor
 * @return bool True if successfully recorded, false otherwise
 */
function recordVisitor($profileId, $visitorId) {
    global $db;
    
    // Don't record if user is viewing their own profile or visitor ID is invalid/not logged in
    if ($profileId == $visitorId || $visitorId <= 0 || $visitorId == 185753) {
        return false;
    }
    
    try {
        // Update visit time if visitor already exists, insert new record otherwise
        // Using UTC_TIMESTAMP() instead of NOW() to ensure consistent UTC timestamps
        $query = "INSERT INTO profile_visitors (profile_id, visitor_id, visit_time) 
                 VALUES (?, ?, UTC_TIMESTAMP())
                 ON DUPLICATE KEY UPDATE visit_time = UTC_TIMESTAMP()";
        
        $stmt = $db->prepare($query);
        
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $db->error);
        }
        
        $stmt->bind_param('ii', $profileId, $visitorId);
        $result = $stmt->execute();
        $stmt->close();
        
        // Check if there's a recent visit within the last 5 seconds
        $checkRecentQuery = "SELECT 1 FROM visit_history 
                            WHERE profile_id = ? 
                            AND visitor_id = ? 
                            AND visit_time > DATE_SUB(UTC_TIMESTAMP(), INTERVAL 5 SECOND)
                            LIMIT 1";
                            
        $checkStmt = $db->prepare($checkRecentQuery);
        
        if (!$checkStmt) {
            throw new Exception("Prepare failed for recent check: " . $db->error);
        }
        
        $checkStmt->bind_param('ii', $profileId, $visitorId);
        $checkStmt->execute();
        $checkStmt->store_result();
        $recentVisitExists = $checkStmt->num_rows > 0;
        $checkStmt->close();
        
        $historyResult = true;
        
        // Only insert into visit_history if there's no recent visit
        if (!$recentVisitExists) {
            // Insert a new record in the visit history table for streak tracking
            $historyQuery = "INSERT INTO visit_history (profile_id, visitor_id, visit_time) 
                            VALUES (?, ?, UTC_TIMESTAMP())";
            
            $historyStmt = $db->prepare($historyQuery);
            
            if (!$historyStmt) {
                throw new Exception("Prepare failed for history: " . $db->error);
            }
            
            $historyStmt->bind_param('ii', $profileId, $visitorId);
            $historyResult = $historyStmt->execute();
            $historyStmt->close();
        }
        
        return $result && $historyResult;
    } catch (Exception $e) {
        error_log("Error recording visitor: " . $e->getMessage());
        return false;
    }
}

/**
 * Get recent visitors for a profile
 * 
 * @param int $profileId The ID of the profile
 * @param int $limit Maximum number of visitors to retrieve
 * @return array Array of visitor records
 */
function getRecentVisitors($profileId, $limit = 10) {
    global $db;
    
    try {
        $query = "SELECT visitor_id, visit_time
                 FROM profile_visitors
                 WHERE profile_id = ?
                 ORDER BY visit_time DESC
                 LIMIT ?";
        
        $stmt = $db->prepare($query);
        
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $db->error);
        }
        
        $stmt->bind_param('ii', $profileId, $limit);
        $stmt->execute();
        
        $result = $stmt->get_result();
        $visitors = [];
        
        while ($row = $result->fetch_assoc()) {
            $visitors[] = $row;
        }
        
        $stmt->close();
        return $visitors;
    } catch (Exception $e) {
        error_log("Error retrieving visitors: " . $e->getMessage());
        return [];
    }
}

/**
 * Calculate the visit streak for a specific visitor
 * 
 * @param int $profileId The ID of the profile
 * @param int $visitorId The ID of the visitor
 * @return int The number of consecutive days the visitor has visited
 */
function calculateVisitStreak($profileId, $visitorId) {
    global $db;
    
    try {
        // SQL query using session variables to calculate the current streak
        $query = "
            SELECT COUNT(*) AS current_streak
            FROM (
                SELECT
                    visit_date,
                    -- Assign group number: increment if date diff > 1 day
                    @streak_group := IF(@prev_date IS NULL OR DATEDIFF(@prev_date, visit_date) = 1, @streak_group, @streak_group + 1) AS streak_group,
                    @prev_date := visit_date
                FROM (
                    -- Get distinct visit dates in descending order
                    SELECT DISTINCT DATE(visit_time) AS visit_date
                    FROM visit_history
                    WHERE profile_id = ? AND visitor_id = ?
                    ORDER BY visit_date DESC
                ) AS distinct_visits,
                (SELECT @prev_date := NULL, @streak_group := 0) AS vars -- Initialize session variables
                ORDER BY visit_date DESC -- Order is important for variable assignment logic
            ) AS grouped_visits
            -- Filter for the most recent streak group (group 0)
            WHERE streak_group = 0;
        ";
        
        $stmt = $db->prepare($query);
        
        if (!$stmt) {
            // Log the specific prepare error
            throw new Exception("Prepare failed: (" . $db->errno . ") " . $db->error);
        }
        
        $stmt->bind_param('ii', $profileId, $visitorId);
        $stmt->execute();
        
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $stmt->close();
        
        // Return the streak count, or 0 if no rows/result
        return $row ? (int)$row['current_streak'] : 0;
        
    } catch (Exception $e) {
        error_log("Error calculating visit streak for profile {$profileId}, visitor {$visitorId}: " . $e->getMessage());
        return 0; // Return 0 on error
    }
}
?> 
<?php
// Top visitors over a period
// Params: profile_id (int), days (int, default 30), limit (int, default 10)

date_default_timezone_set('UTC');
require_once __DIR__ . '/common.php';
vb_enable_cors_json(['https://gamebanana.com']);
header('Content-Type: application/json');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') { http_response_code(200); exit; }

// Enforce rate limiting (30 requests per 5 minute)
vb_enforce_rate_limit(30, 300);

require_once 'db_config.php';
require_once 'gamebanana_api.php';
require_once 'track_visitor.php';

vb_require_allowed_origin(['https://gamebanana.com']);

// Authenticate app access if user is logged in
$visitorId = isset($_GET['_idMember']) ? (int)$_GET['_idMember'] : 0;
$profileId = isset($_GET['profile_id']) ? (int)$_GET['profile_id'] : 0;

if ($profileId > 0) {
    vb_require_app_authentication($profileId, 487);
}

$days = isset($_GET['days']) ? (int)$_GET['days'] : 30; // 0 = lifetime
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
$offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
if ($limit < 1 || $limit > 50) { $limit = 10; }
if ($profileId <= 0) { vb_json_response(['error' => 'Invalid profile ID'], 400); }
if ($days < 0) { $days = 30; }

try {
    global $db;
    $sinceExpr = "DATE_SUB(UTC_TIMESTAMP(), INTERVAL ? DAY)";

    if ($days === 0) {
        $sql = "SELECT visitor_id,
                       COUNT(*) AS visits,
                       COUNT(DISTINCT DATE(visit_time)) AS unique_days,
                       MAX(visit_time) AS last_visit
                FROM visit_history
                WHERE profile_id = ?
                GROUP BY visitor_id
                ORDER BY unique_days DESC, visits DESC
                LIMIT ? OFFSET ?";
        $stmt = $db->prepare($sql);
        if (!$stmt) { throw new Exception('Prepare failed: ' . $db->error); }
        $stmt->bind_param('iii', $profileId, $limit, $offset);
    } else {
        $sql = "SELECT visitor_id,
                       COUNT(*) AS visits,
                       COUNT(DISTINCT DATE(visit_time)) AS unique_days,
                       MAX(visit_time) AS last_visit
                FROM visit_history
                WHERE profile_id = ? AND visit_time >= $sinceExpr
                GROUP BY visitor_id
                ORDER BY unique_days DESC, visits DESC
                LIMIT ? OFFSET ?";
        $stmt = $db->prepare($sql);
        if (!$stmt) { throw new Exception('Prepare failed: ' . $db->error); }
        $stmt->bind_param('iiii', $profileId, $days, $limit, $offset);
    }
    $stmt->execute();
    $res = $stmt->get_result();

    $rows = [];
    $ids = [];
    while ($row = $res->fetch_assoc()) {
        $vid = (int)$row['visitor_id'];
        if ($vid <= 0) continue;
        $rows[] = [
            'visitor_id' => $vid,
            'visits' => (int)$row['visits'],
            'unique_days' => (int)$row['unique_days'],
            'last_visit' => $row['last_visit'] ?? null
        ];
        $ids[] = $vid;
    }
    $stmt->close();

    // Enrich with member details (cached)
    $details = [];
    foreach ($ids as $id) {
        $d = getMemberDetails($id);
        if ($d) { $details[$id] = $d; }
    }

    $out = [];
    foreach ($rows as $r) {
        $m = $details[$r['visitor_id']] ?? [
            'id' => $r['visitor_id'],
            'name' => 'Member_' . $r['visitor_id'],
            'avatar' => 'https://gamebanana.com/img/default_avatar.png'
        ];
        // compute current streak (costly but OK for small page size)
        $currentStreak = 0;
        try { $currentStreak = calculateVisitStreak($profileId, $r['visitor_id']); } catch (Exception $e) { $currentStreak = 0; }
        $out[] = [
            'visitor' => $m,
            'visits' => $r['visits'],
            'unique_days' => $r['unique_days'],
            'current_streak' => $currentStreak,
            'last_visit' => $r['last_visit']
        ];
    }

    vb_json_response($out, 200, 120);
} catch (Exception $e) {
    error_log('Top visitors error: ' . $e->getMessage());
    vb_json_response(['error' => 'Server error'], 500);
}

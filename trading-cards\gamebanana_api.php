<?php
/**
 * GameBanana API integration for Trading Cards app
 * Extends the visitors app API functionality
 */

// Include the visitors API functions
require_once __DIR__ . '/../visitors/gamebanana_api.php';

/**
 * Get member details specifically for trading cards
 * Uses the same caching as visitors app but with additional card-specific data
 */
function tc_getMemberDetails($memberId) {
    return getMemberDetails($memberId);
}

/**
 * Get multiple members' details for trading cards
 */
function tc_getMultipleMemberDetails($memberIds) {
    return getMultipleMemberDetails($memberIds);
}

/**
 * Get member avatar URL with fallback
 */
function tc_getMemberAvatar($memberId) {
    $member = tc_getMemberDetails($memberId);
    return $member ? $member['avatar'] : 'https://gamebanana.com/img/default_avatar.png';
}

/**
 * Get member name with fallback
 */
function tc_getMemberName($memberId) {
    $member = tc_getMemberDetails($memberId);
    return $member ? $member['name'] : "Member_{$memberId}";
}
?>

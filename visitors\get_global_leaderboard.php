<?php
// Global Leaderboard: top visitors across ALL profiles
// Params: days (int, default 30, 0 = lifetime), limit (int, default 10), offset (int, default 0)

date_default_timezone_set('UTC');
require_once __DIR__ . '/common.php';
vb_enable_cors_json(['https://gamebanana.com']);
header('Content-Type: application/json');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') { http_response_code(200); exit; }

// Enforce rate limiting (30 requests per 5 minute)
vb_enforce_rate_limit(30, 300);

require_once 'db_config.php';
require_once 'gamebanana_api.php';

vb_require_allowed_origin(['https://gamebanana.com']);

// Authenticate app access if user is logged in
$profileId = isset($_GET['_idProfile']) ? (int)$_GET['_idProfile'] : 0;
if ($profileId > 0) {
    vb_require_app_authentication($profileId, 487);
}

$days = isset($_GET['days']) ? (int)$_GET['days'] : 30; // 0=lifetime
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
$offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
if ($limit < 1 || $limit > 50) { $limit = 10; }
if ($offset < 0) { $offset = 0; }
if ($days < 0) { $days = 30; }

try {
    global $db;

    if ($days === 0) {
        $sql = "SELECT profile_id,
                       COUNT(*) AS visits,
                       COUNT(DISTINCT DATE(visit_time)) AS unique_days,
                       MAX(visit_time) AS last_visit
                FROM visit_history
                GROUP BY profile_id
                ORDER BY unique_days DESC, visits DESC
                LIMIT ? OFFSET ?";
        $stmt = $db->prepare($sql);
        if (!$stmt) { 
            throw new Exception('Prepare failed: ' . $db->error); 
        }
        $stmt->bind_param('ii', $limit, $offset);
    } else {
        $sql = "SELECT profile_id,
                       COUNT(*) AS visits,
                       COUNT(DISTINCT DATE(visit_time)) AS unique_days,
                       MAX(visit_time) AS last_visit
                FROM visit_history
                WHERE visit_time >= DATE_SUB(UTC_TIMESTAMP(), INTERVAL ? DAY)
                GROUP BY profile_id
                ORDER BY unique_days DESC, visits DESC
                LIMIT ? OFFSET ?";
        $stmt = $db->prepare($sql);
        if (!$stmt) { 
            throw new Exception('Prepare failed: ' . $db->error); 
        }
        $stmt->bind_param('iii', $days, $limit, $offset);
    }

    if (!$stmt->execute()) {
        throw new Exception('Execute failed: ' . $stmt->error);
    }
    $res = $stmt->get_result();

    $rows = [];
    $ids = [];
    while ($row = $res->fetch_assoc()) {
        $pid = (int)$row['profile_id'];
        if ($pid <= 0) continue;
        $rows[] = [
            'profile_id' => $pid,
            'visits' => (int)$row['visits'],
            'unique_days' => (int)$row['unique_days'],
            'last_visit' => $row['last_visit'] ?? null
        ];
        $ids[] = $pid;
    }
    $stmt->close();

    // Enrich profile owner details
    $out = [];
    foreach ($rows as $r) {
        $profileOwner = getMemberDetails($r['profile_id']);
        if (!$profileOwner) {
            $profileOwner = [
                'id' => $r['profile_id'],
                'name' => 'Member_' . $r['profile_id'],
                'avatar' => 'https://gamebanana.com/img/default_avatar.png'
            ];
        }
        // Compute current streak for this profile (consecutive UTC days with visits)
        $currentStreak = 0;
        try {
            $st = $db->prepare("SELECT DISTINCT DATE(visit_time) AS d FROM visit_history WHERE profile_id = ? ORDER BY d DESC");
            if ($st) {
                $st->bind_param('i', $r['profile_id']);
                $st->execute();
                $rs = $st->get_result();
                $prev = null;
                while ($row2 = $rs->fetch_assoc()) {
                    $d = new DateTime($row2['d']);
                    if ($prev === null) {
                        $currentStreak = 1;
                        $prev = $d;
                        continue;
                    }
                    $diff = $prev->diff($d)->days;
                    if ($diff === 1) {
                        $currentStreak++;
                        $prev = $d;
                    } else if ($diff > 1) {
                        break;
                    }
                }
                $st->close();
            }
        } catch (Exception $e) {
            $currentStreak = 0;
        }

        $out[] = [
            'visitor' => $profileOwner, // Frontend expects 'visitor' field
            'profile_owner' => $profileOwner,
            'visits' => $r['visits'],
            'unique_days' => $r['unique_days'],
            'current_streak' => $currentStreak,
            'last_visit' => $r['last_visit']
        ];
    }

    vb_json_response($out, 200, 120);
} catch (Exception $e) {
    error_log('Global leaderboard error: ' . $e->getMessage());
    vb_json_response(['error' => 'Server error'], 500);
}

# GameBanana Trading Cards App

A collectible trading card system for GameBanana profiles that allows members to collect cards from other users' profiles.

## Features

### Core Functionality
- **Unique Member Cards**: Each GameBanana member gets a unique trading card with their avatar and a randomly assigned rarity
- **Rarity System**: 5 rarity levels with different spawn rates and visual styles
  - Common (60% chance): 30-60 minute spawn rate
  - Uncommon (25% chance): 2-4 hour spawn rate  
  - Rare (10% chance): 6-12 hour spawn rate
  - Epic (4% chance): 24-48 hour spawn rate
  - Legendary (1% chance): 3-7 day spawn rate
- **Spawn Mechanics**: Cards appear on profiles based on hidden cooldown timers
- **Collection System**: Members can collect cards from other profiles when they're spawned
- **Collection Tracking**: View your collected cards with statistics and rarity breakdown

### User Interface
- **GameBanana Integration**: Seamlessly matches GameBanana's styling and module structure
- **Tabbed Interface**: 
  - Member Card: Shows the profile owner's card and collection status
  - My Collection: Personal collection with statistics
  - Discover: Recently available cards from other members
- **Real-time Updates**: Auto-refresh spawn status and available cards
- **Responsive Design**: Works on desktop and mobile devices

### Technical Features
- **AJAX-powered**: Smooth interactions without page reloads
- **Rate Limiting**: Prevents abuse with configurable request limits
- **Caching**: Efficient API calls with intelligent caching
- **Admin Panel**: Management interface for monitoring and debugging
- **Spawn Manager**: Automated background process for managing card spawns

## Installation

### 1. Database Setup
Run the SQL script to create the required tables:
```sql
-- Execute the contents of db_setup.sql
```

### 2. File Structure
```
trading-cards/
├── main.php              # Main app interface
├── trading-cards.css     # Styling
├── db_config.php         # Database configuration
├── common.php            # Shared utilities
├── card_system.php       # Core card logic
├── gamebanana_api.php    # API integration
├── collect_card.php      # Card collection endpoint
├── get_collection.php    # Collection data endpoint
├── get_discover.php      # Discovery endpoint
├── get_member_card.php   # Member card endpoint
├── check_spawn_status.php # Spawn status endpoint
├── admin.php             # Admin interface
├── spawn_manager.php     # Automated spawn management
└── cache/                # Caching directory
```

### 3. Configuration
1. Update database credentials in `db_config.php`
2. Set the correct CSS URL in `main.php` (line 2)
3. Configure the GameBanana app ID (currently 488) for authentication
4. Set up the spawn manager as a cron job (recommended every 5-10 minutes)

### 4. GameBanana Integration
Add the app to GameBanana profiles by embedding:
```html
<iframe src="https://your-domain.com/gamebanana/trading-cards/main.php?_idProfile={PROFILE_ID}&_idMember={MEMBER_ID}"></iframe>
```

## Usage

### For Members
1. **View Cards**: Visit any profile to see their trading card
2. **Collect Cards**: Click "Collect Card" when a card is spawned (available)
3. **View Collection**: Use the "My Collection" tab to see collected cards and statistics
4. **Discover Cards**: Use the "Discover" tab to find recently available cards

### For Administrators
1. **Admin Panel**: Access via `admin.php?admin_key=tc_admin_2024`
2. **Force Spawn/Despawn**: Manually control card availability
3. **Change Rarity**: Modify card rarities for testing or balancing
4. **View Statistics**: Monitor system usage and card distribution
5. **Spawn Manager**: Run `spawn_manager.php` to update spawn statuses

## API Endpoints

### Collection
- `POST collect_card.php` - Collect a card
- `GET get_collection.php` - Get member's collection
- `GET get_discover.php` - Get available cards

### Card Management  
- `GET get_member_card.php` - Get specific member's card
- `GET check_spawn_status.php` - Check card spawn status

## Security Features

- **Origin Validation**: Only accepts requests from GameBanana.com
- **Rate Limiting**: Prevents API abuse
- **Authentication**: Uses GameBanana's app authentication system
- **Input Validation**: Sanitizes all user inputs
- **SQL Injection Protection**: Uses prepared statements

## Customization

### Rarity Configuration
Edit the `CARD_RARITIES` constant in `common.php` to modify:
- Spawn rates
- Rarity weights (drop chances)
- Colors and styling
- Names and descriptions

### Styling
Modify `trading-cards.css` to customize:
- Card appearance
- Rarity colors
- Animations and effects
- Layout and spacing

### Spawn Rates
Adjust spawn timing by modifying the min/max spawn minutes in the rarity configuration.

## Maintenance

### Regular Tasks
1. Run `spawn_manager.php` every 5-10 minutes via cron
2. Monitor database size and clean old logs periodically
3. Check admin panel for system statistics
4. Update member cache as needed

### Troubleshooting
- Check error logs for API failures
- Verify GameBanana authentication is working
- Ensure database connections are stable
- Monitor rate limiting for abuse

## Future Enhancements

Potential features for future versions:
- Trading system between members
- Card evolution/upgrading
- Special event cards
- Achievements and badges
- Card marketplace
- Mobile app integration

## Support

For issues or questions:
1. Check the admin panel for system status
2. Review error logs for specific issues
3. Verify GameBanana API connectivity
4. Test with the spawn manager script

<?php
/**
 * Admin interface for managing trading cards
 * Simple admin panel for debugging and management
 */

require_once 'common.php';
require_once 'db_config.php';
require_once 'card_system.php';

// Simple authentication - in production, use proper admin authentication
$adminKey = $_GET['admin_key'] ?? '';
if ($adminKey !== 'tc_admin_2024') {
    die('Access denied');
}

// Handle actions
$action = $_GET['action'] ?? '';
$message = '';

if ($action === 'force_spawn' && isset($_GET['member_id'])) {
    $memberId = (int)$_GET['member_id'];
    global $db;
    $query = "UPDATE member_cards SET is_spawned = 1, last_spawn_time = UTC_TIMESTAMP() WHERE member_id = ?";
    $stmt = $db->prepare($query);
    $stmt->bind_param('i', $memberId);
    if ($stmt->execute()) {
        tc_logSpawnEvent($memberId);
        $message = "Forced spawn for member $memberId";
    }
}

if ($action === 'force_despawn' && isset($_GET['member_id'])) {
    $memberId = (int)$_GET['member_id'];
    global $db;
    $query = "UPDATE member_cards SET is_spawned = 0 WHERE member_id = ?";
    $stmt = $db->prepare($query);
    $stmt->bind_param('i', $memberId);
    if ($stmt->execute()) {
        $message = "Forced despawn for member $memberId";
    }
}

if ($action === 'change_rarity' && isset($_GET['member_id']) && isset($_GET['rarity'])) {
    $memberId = (int)$_GET['member_id'];
    $rarity = $_GET['rarity'];
    if (array_key_exists($rarity, CARD_RARITIES)) {
        $spawnRate = tc_generate_spawn_rate($rarity);
        global $db;
        $query = "UPDATE member_cards SET rarity = ?, spawn_rate_minutes = ? WHERE member_id = ?";
        $stmt = $db->prepare($query);
        $stmt->bind_param('sii', $rarity, $spawnRate, $memberId);
        if ($stmt->execute()) {
            $message = "Changed rarity for member $memberId to $rarity";
        }
    }
}

// Get statistics
global $db;
$statsQuery = "SELECT 
    COUNT(*) as total_cards,
    SUM(CASE WHEN rarity = 'common' THEN 1 ELSE 0 END) as common_count,
    SUM(CASE WHEN rarity = 'uncommon' THEN 1 ELSE 0 END) as uncommon_count,
    SUM(CASE WHEN rarity = 'rare' THEN 1 ELSE 0 END) as rare_count,
    SUM(CASE WHEN rarity = 'epic' THEN 1 ELSE 0 END) as epic_count,
    SUM(CASE WHEN rarity = 'legendary' THEN 1 ELSE 0 END) as legendary_count,
    SUM(CASE WHEN is_spawned = 1 THEN 1 ELSE 0 END) as spawned_count,
    SUM(total_collections) as total_collections
FROM member_cards";
$statsResult = $db->query($statsQuery);
$stats = $statsResult->fetch_assoc();

// Get recent cards
$recentQuery = "SELECT mc.*, 
    (SELECT COUNT(*) FROM card_collections WHERE card_owner_id = mc.member_id) as collection_count
FROM member_cards mc 
ORDER BY mc.created_at DESC 
LIMIT 20";
$recentResult = $db->query($recentQuery);
$recentCards = [];
while ($row = $recentResult->fetch_assoc()) {
    $row['member'] = tc_getMemberDetails($row['member_id']);
    $recentCards[] = $row;
}

// Get collection stats
$collectionQuery = "SELECT 
    COUNT(*) as total_collections,
    COUNT(DISTINCT collector_id) as unique_collectors,
    COUNT(DISTINCT card_owner_id) as unique_cards_collected
FROM card_collections";
$collectionResult = $db->query($collectionQuery);
$collectionStats = $collectionResult->fetch_assoc();
?>

<!DOCTYPE html>
<html>
<head>
    <title>Trading Cards Admin</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
        .stat-value { font-size: 24px; font-weight: bold; color: #333; }
        .stat-label { font-size: 14px; color: #666; margin-top: 5px; }
        .card-list { margin-top: 20px; }
        .card-item { background: #f8f9fa; padding: 15px; margin-bottom: 10px; border-radius: 6px; display: flex; align-items: center; gap: 15px; }
        .card-avatar { width: 40px; height: 40px; border-radius: 50%; }
        .card-info { flex: 1; }
        .card-actions { display: flex; gap: 10px; }
        .btn { padding: 5px 10px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; font-size: 12px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .rarity { padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: bold; text-transform: uppercase; }
        .rarity.common { background: #757575; color: white; }
        .rarity.uncommon { background: #2e7d32; color: white; }
        .rarity.rare { background: #1565c0; color: white; }
        .rarity.epic { background: #6a1b9a; color: white; }
        .rarity.legendary { background: #e65100; color: white; }
        .message { background: #d4edda; color: #155724; padding: 10px; border-radius: 4px; margin-bottom: 20px; }
        .spawned { color: #28a745; font-weight: bold; }
        .not-spawned { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Trading Cards Admin Panel</h1>
        
        <?php if ($message): ?>
        <div class="message"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <h2>System Statistics</h2>
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value"><?php echo $stats['total_cards']; ?></div>
                <div class="stat-label">Total Cards</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo $stats['spawned_count']; ?></div>
                <div class="stat-label">Currently Spawned</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo $collectionStats['total_collections']; ?></div>
                <div class="stat-label">Total Collections</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo $collectionStats['unique_collectors']; ?></div>
                <div class="stat-label">Unique Collectors</div>
            </div>
        </div>
        
        <h3>Rarity Distribution</h3>
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value"><?php echo $stats['common_count']; ?></div>
                <div class="stat-label">Common</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo $stats['uncommon_count']; ?></div>
                <div class="stat-label">Uncommon</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo $stats['rare_count']; ?></div>
                <div class="stat-label">Rare</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo $stats['epic_count']; ?></div>
                <div class="stat-label">Epic</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo $stats['legendary_count']; ?></div>
                <div class="stat-label">Legendary</div>
            </div>
        </div>
        
        <h2>Recent Cards</h2>
        <div class="card-list">
            <?php foreach ($recentCards as $card): ?>
            <div class="card-item">
                <img src="<?php echo $card['member']['avatar']; ?>" alt="Avatar" class="card-avatar">
                <div class="card-info">
                    <strong><?php echo htmlspecialchars($card['member']['name']); ?></strong> (ID: <?php echo $card['member_id']; ?>)<br>
                    <span class="rarity <?php echo $card['rarity']; ?>"><?php echo ucfirst($card['rarity']); ?></span>
                    <span class="<?php echo $card['is_spawned'] ? 'spawned' : 'not-spawned'; ?>">
                        <?php echo $card['is_spawned'] ? 'SPAWNED' : 'Not spawned'; ?>
                    </span>
                    <small>(<?php echo $card['spawn_rate_minutes']; ?>min rate, <?php echo $card['total_collections']; ?> collections)</small>
                </div>
                <div class="card-actions">
                    <?php if ($card['is_spawned']): ?>
                    <a href="?admin_key=tc_admin_2024&action=force_despawn&member_id=<?php echo $card['member_id']; ?>" class="btn btn-danger">Despawn</a>
                    <?php else: ?>
                    <a href="?admin_key=tc_admin_2024&action=force_spawn&member_id=<?php echo $card['member_id']; ?>" class="btn btn-success">Force Spawn</a>
                    <?php endif; ?>
                    
                    <select onchange="changeRarity(<?php echo $card['member_id']; ?>, this.value)">
                        <option value="">Change Rarity</option>
                        <option value="common" <?php echo $card['rarity'] === 'common' ? 'selected' : ''; ?>>Common</option>
                        <option value="uncommon" <?php echo $card['rarity'] === 'uncommon' ? 'selected' : ''; ?>>Uncommon</option>
                        <option value="rare" <?php echo $card['rarity'] === 'rare' ? 'selected' : ''; ?>>Rare</option>
                        <option value="epic" <?php echo $card['rarity'] === 'epic' ? 'selected' : ''; ?>>Epic</option>
                        <option value="legendary" <?php echo $card['rarity'] === 'legendary' ? 'selected' : ''; ?>>Legendary</option>
                    </select>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    
    <script>
    function changeRarity(memberId, rarity) {
        if (rarity) {
            window.location.href = `?admin_key=tc_admin_2024&action=change_rarity&member_id=${memberId}&rarity=${rarity}`;
        }
    }
    </script>
</body>
</html>

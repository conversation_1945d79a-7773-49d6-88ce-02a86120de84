<?php
/**
 * Helper functions for interacting with the GameBanana API
 */

// Define cache settings
define('CACHE_DIR', __DIR__ . '/cache/');
define('CACHE_DURATION', 21600); // Cache duration 6 hours

// Ensure cache directory exists and is writable
if (!is_dir(CACHE_DIR)) {
    mkdir(CACHE_DIR, 0755, true); // Create if not exists
}
if (!is_writable(CACHE_DIR)) {
    error_log("Cache directory is not writable: " . CACHE_DIR);
    // Consider adding a fallback or throwing an error if cache is critical
}

/**
 * Get member details from GameBanana API with caching
 * 
 * @param int $memberId The GameBanana member ID
 * @return array|null Member information or null if not found
 */
function getMemberDetails($memberId) {
    // Sanitize memberId just in case
    $memberId = (int)$memberId;
    if ($memberId <= 0) {
        return null;
    }

    $cacheFile = CACHE_DIR . 'member_' . $memberId . '.cache';

    // Check cache
    if (is_writable(CACHE_DIR) && file_exists($cacheFile) && (time() - filemtime($cacheFile)) < CACHE_DURATION) {
        $cachedData = file_get_contents($cacheFile);
        if ($cachedData !== false) {
            $data = unserialize($cachedData);
            if ($data !== false) {
                // Optional: Log cache hit for debugging
                // error_log("Cache hit for member ID: {$memberId}");
                return $data; 
            } else {
                 error_log("Failed to unserialize cache data for member ID: {$memberId}");
            }
        } else {
            error_log("Failed to read cache file: {$cacheFile}");
        }
    }
    
    // Optional: Log cache miss for debugging
    // error_log("Cache miss for member ID: {$memberId}");

    // --- Cache miss or invalid cache - Fetch from API --- 
    $apiUrl = "https://gamebanana.com/apiv10/Member/{$memberId}";
    
    // Use cURL if available, otherwise fallback to file_get_contents
    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_USERAGENT, 'GameBanana App Visitor Tracker');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        // Handle curl errors
        if ($curlError) {
            error_log("cURL Error: {$curlError} when fetching member {$memberId}");
            return null;
        }
        
        // Handle API errors
        if ($httpCode !== 200) {
            error_log("API Error: Failed to get member details for ID {$memberId}. HTTP Code: {$httpCode}, Response: " . substr($response, 0, 100));
            return null;
        }
    } else {
        // Fallback to file_get_contents
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => 'User-Agent: GameBanana App Visitor Tracker',
                'timeout' => 5
            ]
        ]);
        $response = @file_get_contents($apiUrl, false, $context);
        if ($response === false) {
            error_log("file_get_contents Error: Failed to fetch member {$memberId}");
            return null;
        }
        $httpCode = 200; // Assume success if we got data
    }
    
    $data = json_decode($response, true);
    
    // Log raw response for debugging if JSON decode fails
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("JSON Decode Error: " . json_last_error_msg() . " for member {$memberId}. Raw response: " . substr($response, 0, 200));
        return null;
    }
    
    // Ensure data is in expected format
    if (!is_array($data) || empty($data)) {
        error_log("API Data Error: Invalid or empty response for member ID {$memberId}. Response type: " . gettype($data));
        return null;
    }
    
    // Extract the needed information from updated API format
    $memberDetails = [
        'id' => $memberId,
        'name' => isset($data['_sName']) && !empty($data['_sName']) ? $data['_sName'] : "Member_{$memberId}",
        'avatar' => isset($data['_sHdAvatarUrl']) ? $data['_sHdAvatarUrl'] : 
                  (isset($data['_sAvatarUrl']) ? $data['_sAvatarUrl'] : 
                  'https://gamebanana.com/img/default_avatar.png'),
        'upic' => $data['_sUpicUrl'] ?? null,
        'title' => $data['_sUserTitle'] ?? ($data['_sHonoraryTitle'] ?? ''),
        'medal_count' => (
            (isset($data['_aNormalMedals']) ? count($data['_aNormalMedals']) : 0) + 
            (isset($data['_aRareMedals']) ? count($data['_aRareMedals']) : 0) + 
            (isset($data['_aLegendaryMedals']) ? count($data['_aLegendaryMedals']) : 0)
        ),
        'is_founder' => in_array('Founder', $data['_aClearanceLevels'] ?? []),
        'is_uploader' => (
            isset($data['_aClearanceLevels']) && (
                in_array('Contributor', $data['_aClearanceLevels']) || 
                in_array('Super Admin', $data['_aClearanceLevels'])
            )
        ),
        'join_timestamp' => $data['_tsJoinDate'] ?? null,
        'is_online' => isset($data['_bOnline']) ? (bool)$data['_bOnline'] : false,
        'location' => $data['_sLocation'] ?? ''
    ];

    // Write to cache if writable
    if (is_writable(CACHE_DIR)) {
        $cacheWriteResult = file_put_contents($cacheFile, serialize($memberDetails));
        if ($cacheWriteResult === false) {
            error_log("Failed to write to cache file: {$cacheFile}");
        }
    } else {
         // Log if cache dir is still not writable when attempting write
         error_log("Cache directory not writable, cannot save cache for member ID: {$memberId}");
    }

    return $memberDetails;
}

/**
 * Get multiple members' details in a single function call
 * 
 * @param array $memberIds Array of GameBanana member IDs
 * @return array Associative array of member details indexed by member ID
 */
function getMultipleMemberDetails($memberIds) {
    $members = [];
    
    foreach ($memberIds as $memberId) {
        $memberDetails = getMemberDetails($memberId);
        if ($memberDetails) {
            $members[$memberId] = $memberDetails;
        }
    }
    
    return $members;
}
?> 
<?php
// Set the default timezone
date_default_timezone_set('UTC');

// Allow cross-origin requests from GameBanana
require_once __DIR__ . '/common.php';
vb_enable_cors_json(['https://gamebanana.com']);
header("Content-Type: application/json");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    // Just exit with 200 OK status
    http_response_code(200);
    exit;
}

// Enforce rate limiting (30 requests per 5 minute)
vb_enforce_rate_limit(30, 300);

require_once 'db_config.php';
require_once 'track_visitor.php';

// Enforce allowed Origin/Referer
vb_require_allowed_origin(['https://gamebanana.com']);

// Authenticate app access if user is logged in
$profileId = isset($_GET['_idProfile']) ? (int)$_GET['_idProfile'] : 0;
if ($profileId > 0) {
    vb_require_app_authentication($profileId, 487);
}

// Check for required parameters
if (!isset($_GET['_idProfile']) || !isset($_GET['_idMember'])) {
    vb_json_response(['error' => 'Missing required parameters'], 400);
}

$visitorId = (int)$_GET['_idMember'];
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 5;

// Validate IDs
if ($profileId <= 0 || $visitorId <= 0) {
    vb_json_response(['error' => 'Invalid profile or visitor ID'], 400);
}

// Validate limit
if ($limit < 1 || $limit > 50) {
    $limit = 5; // Default to 5 if invalid
}

// Get visit history
try {
    global $db;
    
    $query = "SELECT visit_time 
              FROM visit_history 
              WHERE profile_id = ? AND visitor_id = ? 
              ORDER BY visit_time DESC 
              LIMIT ?";
    
    $stmt = $db->prepare($query);
    
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $db->error);
    }
    
    $stmt->bind_param('iii', $profileId, $visitorId, $limit);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $history = [];
    
    while ($row = $result->fetch_assoc()) {
        // For each visit, calculate what streak it was part of
        $visitTime = $row['visit_time'];
        $visitDate = date('Y-m-d', strtotime($visitTime));
        
        // Get streak at this point in time
        $streak = calculateHistoricalStreak($profileId, $visitorId, $visitDate);
        
        // Format the time/date
        $timeClass = getTimeClass($visitTime);
        $formattedTime = formatTime($visitTime);
        
        $history[] = [
            'visit_time' => $visitTime,
            'formatted_time' => $formattedTime,
            'time_class' => $timeClass,
            'datetime' => date('c', strtotime($visitTime)),
            'streak' => $streak
        ];
    }
    
    vb_json_response($history);
    
} catch (Exception $e) {
    error_log("Error retrieving visit history: " . $e->getMessage());
    vb_json_response(['error' => 'Error retrieving visit history'], 500);
}

/**
 * Calculate the visit streak up to a specific date
 * 
 * @param int $profileId The ID of the profile
 * @param int $visitorId The ID of the visitor
 * @param string $endDate The end date to consider (YYYY-MM-DD)
 * @return int The streak count
 */
function calculateHistoricalStreak($profileId, $visitorId, $endDate) {
    global $db;
    
    try {
        // Get all unique visit dates for this visitor up to the end date
        $query = "SELECT 
                    DISTINCT DATE(visit_time) as visit_date 
                  FROM 
                    visit_history 
                  WHERE 
                    profile_id = ? AND visitor_id = ? AND DATE(visit_time) <= ?
                  ORDER BY 
                    visit_date DESC";
        
        $stmt = $db->prepare($query);
        
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $db->error);
        }
        
        $stmt->bind_param('iis', $profileId, $visitorId, $endDate);
        $stmt->execute();
        
        $result = $stmt->get_result();
        $visitDates = [];
        
        while ($row = $result->fetch_assoc()) {
            $visitDates[] = $row['visit_date'];
        }
        
        $stmt->close();
        
        // Return 0 if no visits
        if (empty($visitDates)) {
            return 0;
        }
        
        // Calculate streak
        $streak = 1; // Start with the current visit
        $currentDate = new DateTime($visitDates[0]);
        
        for ($i = 1; $i < count($visitDates); $i++) {
            $previousDate = new DateTime($visitDates[$i]);
            $dayDiff = $currentDate->diff($previousDate)->days;
            
            // If dates are consecutive (1 day apart)
            if ($dayDiff == 1) {
                $streak++;
                $currentDate = $previousDate;
            } else {
                // Streak is broken
                break;
            }
        }
        
        return $streak;
        
    } catch (Exception $e) {
        error_log("Error calculating historical visit streak: " . $e->getMessage());
        return 0;
    }
}

/**
 * Format time similar to GameBanana
 */
function formatTime($timestamp) { return vb_format_time($timestamp); }

/**
 * Get time class similar to GameBanana
 */
function getTimeClass($timestamp) { return vb_time_class($timestamp); }
?> 
<?php
// Set the default timezone
date_default_timezone_set('UTC');

// Allow cross-origin requests from GameBanana
require_once __DIR__ . '/common.php';
vb_enable_cors_json(['https://gamebanana.com']);
header("Content-Type: application/json");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    // Just exit with 200 OK status
    http_response_code(200);
    exit;
}

// Enforce rate limiting (30 requests per 5 minute)
vb_enforce_rate_limit(30, 300);

require_once 'db_config.php';
require_once 'track_visitor.php';
require_once 'gamebanana_api.php';

// Enforce allowed Origin/Referer
vb_require_allowed_origin(['https://gamebanana.com']);

// Authenticate app access if user is logged in
$profileId = isset($_GET['_idProfile']) ? (int)$_GET['_idProfile'] : 0;
if ($profileId > 0) {
    vb_require_app_authentication($profileId, 487);
}

// Check for required parameters
if (!isset($_GET['profile_id']) || !isset($_GET['count'])) {
    vb_json_response(['error' => 'Missing required parameters'], 400);
}

// Get parameters
$profileId = (int)$_GET['profile_id'];
$count = (int)$_GET['count'];

// Validate profile ID
if ($profileId <= 0) {
    vb_json_response(['error' => 'Invalid profile ID'], 400);
}

// Validate count
if ($count < 1 || $count > 50) {
    $count = 10; // Default to 10 if invalid
}

// Get visitors
$visitors = getRecentVisitors($profileId, $count);
$visitorData = [];

// Format visitor data
foreach ($visitors as $visitor) {
    // Skip invalid member IDs
    if ($visitor['visitor_id'] <= 0) {
        continue;
    }
    
    // Calculate visit streak
    $streak = calculateVisitStreak($profileId, $visitor['visitor_id']);
    
    $member = getMemberDetails($visitor['visitor_id']);
    
    // Use fallback data if API call fails
    if (!$member) {
        $member = [
            'id' => $visitor['visitor_id'],
            'name' => 'Member_' . $visitor['visitor_id'],
            'avatar' => 'https://gamebanana.com/img/default_avatar.png',
            'title' => '',
            'medal_count' => 0,
            'is_founder' => false,
            'is_uploader' => false,
            'is_online' => false,
            'location' => ''
        ];
    }
    
    // Format timestamp
    $timestamp = strtotime($visitor['visit_time']);
    $formattedTime = formatTime($visitor['visit_time']);
    $timeClass = getTimeClass($visitor['visit_time']);
    $datetime = date('c', $timestamp);
    
    // Add visitor to array
    $visitorData[] = [
        'visitor' => $member,
        'visit_time' => [
            'raw' => $visitor['visit_time'],
            'formatted' => $formattedTime,
            'class' => $timeClass,
            'datetime' => $datetime
        ],
        'streak' => $streak
    ];
}

// Return JSON data
vb_json_response($visitorData);

// Helper functions (simplified versions of those in main.php)
function formatTime($timestamp) { return vb_format_time($timestamp); }
function getTimeClass($timestamp) { return vb_time_class($timestamp); }
?> 
<?php
// Period-limited stats for a profile
// Params: profile_id (int), days (int, default 30)

date_default_timezone_set('UTC');
require_once __DIR__ . '/common.php';
vb_enable_cors_json(['https://gamebanana.com']);
header('Content-Type: application/json');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') { http_response_code(200); exit; }

// Enforce rate limiting (30 requests per 5 minute)
vb_enforce_rate_limit(30, 300);

require_once 'db_config.php';
require_once 'track_visitor.php';

vb_require_allowed_origin(['https://gamebanana.com']);

// Authenticate app access if user is logged in
$visitorId = isset($_GET['_idMember']) ? (int)$_GET['_idMember'] : 0;
$profileId = isset($_GET['profile_id']) ? (int)$_GET['profile_id'] : 0;

if ($profileId > 0) {
    vb_require_app_authentication($profileId, 487);
}
$days = isset($_GET['days']) ? (int)$_GET['days'] : 30; // 0 means lifetime
if ($profileId <= 0) { vb_json_response(['error' => 'Invalid profile ID'], 400); }
if ($days < 0) { $days = 30; }

try {
    global $db;
    $sinceExpr = "DATE_SUB(UTC_TIMESTAMP(), INTERVAL ? DAY)";

    // Visits
    if ($days === 0) {
        $q1 = $db->prepare("SELECT COUNT(*) AS cnt FROM visit_history WHERE profile_id = ?");
        if (!$q1) { throw new Exception('Prepare q1 failed: ' . $db->error); }
        $q1->bind_param('i', $profileId);
    } else {
        $q1 = $db->prepare("SELECT COUNT(*) AS cnt FROM visit_history WHERE profile_id = ? AND visit_time >= $sinceExpr");
        if (!$q1) { throw new Exception('Prepare q1 failed: ' . $db->error); }
        $q1->bind_param('ii', $profileId, $days);
    }
    $q1->execute();
    $vrow = $q1->get_result()->fetch_assoc();
    $visits = (int)($vrow['cnt'] ?? 0);
    $q1->close();

    // Active days
    if ($days === 0) {
        $q2 = $db->prepare("SELECT COUNT(*) AS days_cnt FROM (SELECT DISTINCT DATE(visit_time) d FROM visit_history WHERE profile_id=?) t");
        if (!$q2) { throw new Exception('Prepare q2 failed: ' . $db->error); }
        $q2->bind_param('i', $profileId);
    } else {
        $q2 = $db->prepare("SELECT COUNT(*) AS days_cnt FROM (SELECT DISTINCT DATE(visit_time) d FROM visit_history WHERE profile_id=? AND visit_time >= $sinceExpr) t");
        if (!$q2) { throw new Exception('Prepare q2 failed: ' . $db->error); }
        $q2->bind_param('ii', $profileId, $days);
    }
    $q2->execute();
    $drow = $q2->get_result()->fetch_assoc();
    $activeDays = (int)($drow['days_cnt'] ?? 0);
    $q2->close();

    // Unique visitors
    if ($days === 0) {
        $q3 = $db->prepare("SELECT COUNT(DISTINCT visitor_id) AS uniq_cnt FROM visit_history WHERE profile_id=?");
        if (!$q3) { throw new Exception('Prepare q3 failed: ' . $db->error); }
        $q3->bind_param('i', $profileId);
    } else {
        $q3 = $db->prepare("SELECT COUNT(DISTINCT visitor_id) AS uniq_cnt FROM visit_history WHERE profile_id=? AND visit_time >= $sinceExpr");
        if (!$q3) { throw new Exception('Prepare q3 failed: ' . $db->error); }
        $q3->bind_param('ii', $profileId, $days);
    }
    $q3->execute();
    $urow = $q3->get_result()->fetch_assoc();
    $uniqueVisitors = (int)($urow['uniq_cnt'] ?? 0);
    $q3->close();

    // Period start/end
    $today = new DateTime('now', new DateTimeZone('UTC'));
    $start = ($days === 0) ? new DateTime('@0') : (clone $today)->modify('-' . ($days - 1) . ' days');

    vb_json_response([
        'profile_id' => $profileId,
        'days' => $days,
        'start_date' => $start->format('Y-m-d'),
        'end_date' => $today->format('Y-m-d'),
        'unique_visitors' => $uniqueVisitors,
        'visits' => $visits,
        'active_days' => $activeDays
    ], 200, 120);
} catch (Exception $e) {
    error_log('Period stats error: ' . $e->getMessage());
    vb_json_response(['error' => 'Server error'], 500);
}
